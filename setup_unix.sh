#!/bin/bash

echo "========================================"
echo "    StarWalk Setup for Unix/Linux/macOS"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.8+ from your package manager or https://python.org"
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python $python_version found. Starting setup..."
echo

# Make setup script executable and run it
chmod +x setup.py
python3 setup.py

echo
echo "Setup completed! You can now use StarWalk."
echo
echo "Quick Start:"
echo "1. Run './run_starwalk.sh' to open the UI"
echo "2. Or use './run_starwalk_cli.sh' for command line"
echo

# Make run scripts executable
if [ -f "run_starwalk.sh" ]; then
    chmod +x run_starwalk.sh
fi

if [ -f "run_starwalk_cli.sh" ]; then
    chmod +x run_starwalk_cli.sh
fi
