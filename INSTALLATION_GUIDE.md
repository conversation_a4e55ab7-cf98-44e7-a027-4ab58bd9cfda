# 🚀 StarWalk Installation Guide

This guide will help you set up the StarWalk dual person walk analysis system on your computer.

## 📋 Prerequisites

- **Python 3.8 or higher** (Download from [python.org](https://python.org))
- **Operating System**: Windows 10+, macOS 10.14+, or Linux
- **Hardware**: Computer with webcam support (for video processing)
- **Storage**: At least 500MB free space

## 🔧 Quick Installation

### For Windows Users

1. **Download Python** (if not installed):
   - Go to [python.org](https://python.org/downloads/)
   - Download Python 3.8+ for Windows
   - **Important**: Check "Add Python to PATH" during installation

2. **Run Setup**:
   ```cmd
   # Double-click this file:
   setup_windows.bat
   ```
   
   Or manually:
   ```cmd
   python setup.py
   ```

3. **Start StarWalk**:
   ```cmd
   # Double-click to run UI:
   run_starwalk.bat
   ```

### For macOS/Linux Users

1. **Ensure Python 3.8+ is installed**:
   ```bash
   python3 --version
   ```

2. **Run Setup**:
   ```bash
   # Make executable and run:
   chmod +x setup_unix.sh
   ./setup_unix.sh
   ```
   
   Or manually:
   ```bash
   python3 setup.py
   ```

3. **Start StarWalk**:
   ```bash
   # Run UI:
   ./run_starwalk.sh
   ```

## 🎯 Manual Installation (Advanced Users)

If you prefer to set up manually:

```bash
# 1. Create virtual environment
python -m venv starwalk_env

# 2. Activate environment
# Windows:
starwalk_env\Scripts\activate
# macOS/Linux:
source starwalk_env/bin/activate

# 3. Install requirements
pip install -r requirements.txt

# 4. Run StarWalk
python starwalk_ui.py
```

## 🖥️ Using the StarWalk UI

### Main Interface Features

1. **📹 Video Selection**:
   - **Dual Person Video**: Video with two people walking side by side
   - **Reference Video**: Single person walking (the "ideal" walk)

2. **⚙️ Analysis Options**:
   - Save analysis plot (PNG)
   - Save comparison video (MP4)

3. **🚀 Controls**:
   - **Start Analysis**: Begin the walk comparison
   - **Open Output Folder**: View generated files
   - **Clear**: Reset all inputs

4. **📊 Results Display**:
   - Real-time analysis results
   - Detailed metrics comparison
   - Winner determination

### How to Use

1. **Select Videos**:
   - Click "Browse" or drag-and-drop video files
   - Dual video should show two people walking
   - Reference video should show one person walking

2. **Configure Options**:
   - Check boxes for desired outputs
   - Default: Both plot and video are saved

3. **Run Analysis**:
   - Click "🚀 Start Analysis"
   - Wait for processing (may take 1-5 minutes)
   - View results in the text area

4. **Access Outputs**:
   - Click "🗂️ Open Output Folder"
   - Find generated analysis files

## 📹 Video Requirements

### Dual Person Video
- **Format**: MP4, AVI, MOV, or MKV
- **Aspect Ratio**: 16:9 recommended (automatically split)
- **Content**: Two people walking side by side
- **Duration**: 5-30 seconds recommended
- **Quality**: Clear visibility of both people

### Reference Video
- **Format**: MP4, AVI, MOV, or MKV
- **Aspect Ratio**: Any (automatically cropped to 8:9)
- **Content**: Single person walking
- **Duration**: 5-30 seconds recommended
- **Quality**: Clear visibility of the person

### General Tips
- **Lighting**: Good lighting conditions
- **Camera**: Stable camera (minimal shaking)
- **Pose**: People walking towards/away or sideways
- **Background**: Uncluttered background preferred

## 🔍 Command Line Usage

For advanced users who prefer command line:

```bash
# Activate environment first
source starwalk_env/bin/activate  # macOS/Linux
# or
starwalk_env\Scripts\activate     # Windows

# Run analysis
python starwalk.py dual_video.mp4 reference_video.mp4

# With options
python starwalk.py dual_video.mp4 reference_video.mp4 --save-plot --save-video
```

## 📊 Understanding Results

### Metrics Explained

1. **Similarity Score** (40% weight): How similar poses are to reference
2. **Stride Consistency** (25% weight): Regularity of walking pattern
3. **Posture Stability** (20% weight): Body alignment and balance
4. **Arm Coordination** (15% weight): Natural arm swing patterns
5. **Overall Confidence**: Combined score determining winner

### Performance Grades
- **A+ (0.9-1.0)**: Excellent ⭐⭐⭐⭐⭐
- **A (0.8-0.9)**: Very Good ⭐⭐⭐⭐
- **B+ (0.7-0.8)**: Good ⭐⭐⭐
- **B (0.6-0.7)**: Above Average ⭐⭐
- **C+ (0.5-0.6)**: Average ⭐
- **C (<0.5)**: Needs Improvement

## 🛠️ Troubleshooting

### Common Issues

1. **"Python not found"**:
   - Install Python from python.org
   - Ensure "Add to PATH" was checked during installation
   - Restart command prompt/terminal

2. **"Import Error: mediapipe"**:
   - Activate virtual environment first
   - Run: `pip install -r requirements.txt`

3. **"No video files found"**:
   - Check video file paths
   - Ensure videos are in supported formats (MP4, AVI, MOV, MKV)

4. **"Analysis failed"**:
   - Check that people are clearly visible in videos
   - Ensure good lighting and stable camera
   - Try shorter video clips (5-15 seconds)

5. **UI not responding**:
   - Analysis may take time (1-5 minutes)
   - Check progress bar and status messages
   - Don't close the application during analysis

### Performance Tips

- **Shorter videos** process faster (5-15 seconds ideal)
- **Good lighting** improves pose detection accuracy
- **Stable camera** reduces processing errors
- **Clear visibility** of people improves results

## 📞 Support

If you encounter issues:

1. Check this troubleshooting section
2. Review the README_StarWalk.md file
3. Ensure all requirements are properly installed
4. Try with different video files to isolate issues

## 🎯 Next Steps

After installation:

1. **Test with sample videos** to familiarize yourself
2. **Read README_StarWalk.md** for detailed documentation
3. **Experiment with different walking styles** for comparison
4. **Share results** and provide feedback for improvements

Happy analyzing! 🌟
