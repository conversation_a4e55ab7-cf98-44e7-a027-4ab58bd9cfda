{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8d95c2f8-9fc5-45e1-abdb-240bbe10b138", "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mediapipe import solutions\n", "import math\n", "import os\n", "import glob"]}, {"cell_type": "code", "execution_count": 3, "id": "e312b750-e78f-4fab-bfd9-c7b9d6d0623f", "metadata": {}, "outputs": [], "source": ["import base64\n", "import vertexai\n", "from vertexai.generative_models import GenerativeModel, Part, FinishReason\n", "import vertexai.preview.generative_models as generative_models"]}, {"cell_type": "code", "execution_count": 4, "id": "d142839f-14ba-48f9-9e92-378751a4af97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The latest video file is: /run/user/1001/gvfs/mtp:host=OnePlus_KALAMA-MTP_CID%3A0437_SN%3AD18B65C7_d18b65c7/Internal shared storage/DCIM/Camera/VID20240705172912.mp4\n"]}], "source": ["def get_latest_video_path(directory, extensions=[\".mp4\", \".avi\", \".mov\", \".mkv\"]):\n", "    # Get all files in the directory with the given extensions\n", "    files = []\n", "    for ext in extensions:\n", "        files.extend(glob.glob(os.path.join(directory, f\"*{ext}\")))\n", "\n", "    # Check if there are any files\n", "    if not files:\n", "        print(\"No video files found in the directory.\")\n", "        return None\n", "\n", "    # Sort files by modification time\n", "    files.sort(key=os.path.getmtime, reverse=True)\n", "\n", "    # Get the path of the latest file\n", "    latest_file = files[0]\n", "    return latest_file\n", "\n", "# Example usage\n", "directory = r'/run/user/1001/gvfs/mtp:host=OnePlus_KALAMA-MTP_CID%3A0437_SN%3AD18B65C7_d18b65c7/Internal shared storage/DCIM/Camera'\n", "latest_video_path = get_latest_video_path(directory)\n", "\n", "if latest_video_path:\n", "    print(f\"The latest video file is: {latest_video_path}\")\n", "else:\n", "    print(\"No video file found.\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a02e8747-8f77-4ea7-97c0-9d0fecdf2361", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. <PERSON><PERSON><PERSON>, 2. <PERSON><PERSON><PERSON><PERSON><PERSON>, 3. <PERSON><PERSON><PERSON>, 4. <PERSON>, 5. <PERSON><PERSON>, 6. <PERSON><PERSON>,\n", "7. <PERSON><PERSON>, 8. <PERSON>, 9. <PERSON><PERSON>, 10. <PERSON>, 11. <PERSON><PERSON><PERSON>, 12. <PERSON>, 13. <PERSON><PERSON><PERSON>\n"]}, {"name": "stdin", "output_type": "stream", "text": ["Enter your choice:  2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Model path: /home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Brahmanandham.mp4\n"]}], "source": ["model_path = \"\"\n", "print(\"\"\"1. <PERSON><PERSON><PERSON>, 2. <PERSON><PERSON><PERSON><PERSON><PERSON>, 3. <PERSON><PERSON><PERSON>, 4. <PERSON>, 5. <PERSON><PERSON>, 6. <PERSON><PERSON>,\n", "7. <PERSON><PERSON>, 8. <PERSON>, 9. <PERSON><PERSON>, 10. <PERSON>, 11. <PERSON><PERSON><PERSON>, 12. <PERSON>, 13. <PERSON><PERSON><PERSON>\"\"\")\n", "# Mapping choices to model paths\n", "model_paths = {\n", "    1: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/<PERSON><PERSON><PERSON>_<PERSON>.mp4\",\n", "    2: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Brahmanandham.mp4\",\n", "    3: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>.mp4\",\n", "    4: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Joker.mp4\",\n", "    5: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Modi_1.mp4\",\n", "    6: r\"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/<PERSON><PERSON>_kapoor.mp4\",\n", "    7: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Al<PERSON>_F.mp4\",\n", "    8: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/<PERSON>_<PERSON><PERSON>.mp4\",\n", "    9: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/<PERSON><PERSON>_<PERSON><PERSON>.mp4\",\n", "    10: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/<PERSON>_<PERSON>.mp4\",\n", "    11: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Krit<PERSON>_Sanon.mp4\",\n", "    12: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Tara.mp4\",\n", "    13: r\"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Zendaya.mp4\"\n", "}\n", "\n", "# Getting user choice\n", "choice = int(input(\"Enter your choice: \"))\n", "\n", "# Retrieving the model path based on user choice\n", "model_path = model_paths.get(choice, \"Invalid choice\")\n", "\n", "# Displaying the model path\n", "if model_path != \"Invalid choice\":\n", "    print(f\"Model path: {model_path}\")\n", "else:\n", "    print(model_path)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "acb770d4-e15b-4967-8fe3-9e22452b5bbf", "metadata": {}, "outputs": [], "source": ["# Paths to videos\n", "video_path1 = model_path\n", "video_path2 = latest_video_path\n", "output_video_path = 'CombinedOutput.mp4'\n", "\n", "# Initialize video captures\n", "cap1 = cv2.VideoCapture(video_path1)\n", "cap2 = cv2.VideoCapture(video_path2)\n", "\n", "# Initialize MediaPipe Pose\n", "mp_pose = solutions.pose\n", "pose = mp_pose.Pose()\n", "mp_drawing = solutions.drawing_utils"]}, {"cell_type": "code", "execution_count": 7, "id": "12299d8e-7c0c-4913-8014-b5c4bb00625c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Created TensorFlow Lite XNNPACK delegate for CPU.\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "W0000 00:00:1720180786.528309   16405 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.\n", "W0000 00:00:1720180786.549251   16411 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.\n", "/home/<USER>/anaconda3/envs/rampwalk/lib/python3.12/site-packages/google/protobuf/symbol_database.py:55: UserWarning: SymbolDatabase.GetPrototype() is deprecated. Please use message_factory.GetMessageClass() instead. SymbolDatabase.GetPrototype() will be removed soon.\n", "  warnings.warn('SymbolDatabase.GetPrototype() is deprecated. Please '\n"]}], "source": ["# Function to extract frames and keypoints from a video\n", "def extract_keypoints_and_draw(cap):\n", "    keypoints_all_frames = []\n", "    frames_with_keypoints = []\n", "\n", "    while cap.isOpened():\n", "        ret, frame = cap.read()\n", "        if not ret:\n", "            break\n", "\n", "        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "        results = pose.process(frame_rgb)\n", "\n", "        if results.pose_landmarks:\n", "            keypoints = results.pose_landmarks.landmark\n", "            keypoints_all_frames.append(keypoints)\n", "            mp_drawing.draw_landmarks(frame, results.pose_landmarks, mp_pose.POSE_CONNECTIONS)\n", "\n", "        frames_with_keypoints.append(frame)\n", "\n", "    cap.release()\n", "    return keypoints_all_frames, frames_with_keypoints\n", "\n", "# Extract keypoints and frames from both videos\n", "keypoints_video1, frames_video1 = extract_keypoints_and_draw(cap1)\n", "keypoints_video2, frames_video2 = extract_keypoints_and_draw(cap2)\n", "\n", "# Ensure both videos have the same number of frames\n", "min_frames = min(len(keypoints_video1), len(keypoints_video2))\n", "keypoints_video1 = keypoints_video1[:min_frames]\n", "keypoints_video2 = keypoints_video2[:min_frames]\n", "frames_video1 = frames_video1[:min_frames]\n", "frames_video2 = frames_video2[:min_frames]"]}, {"cell_type": "code", "execution_count": 8, "id": "085d94b4-5eb3-460b-9b5f-080d45ae7965", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Similarity Score: 0.62\n"]}], "source": ["# Function to calculate similarity score\n", "def calculate_similarity_score(keypoints1, keypoints2):\n", "    scores = []\n", "\n", "    for kp1, kp2 in zip(keypoints1, keypoints2):\n", "        frame_score = 0\n", "        for k1, k2 in zip(kp1, kp2):\n", "            frame_score += math.sqrt((k1.x - k2.x) ** 2 + (k1.y - k2.y) ** 2 + (k1.z - k2.z) ** 2)\n", "        scores.append(frame_score / len(kp1))\n", "\n", "    mean_score = np.mean(scores)\n", "    normalized_score = 1 / (1 + mean_score)\n", "    return normalized_score\n", "\n", "# Calculate similarity score\n", "similarity_score = calculate_similarity_score(keypoints_video1, keypoints_video2)\n", "print(f'Similarity Score: {similarity_score:.2f}')"]}, {"cell_type": "code", "execution_count": 9, "id": "c529780f-a9e3-412c-9ce3-c1c407cbc855", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Function to calculate frame-by-frame similarity scores\n", "def frame_similarity_scores(keypoints1, keypoints2):\n", "    frame_scores = []\n", "\n", "    for kp1, kp2 in zip(keypoints1, keypoints2):\n", "        frame_score = 0\n", "        for k1, k2 in zip(kp1, kp2):\n", "            frame_score += math.sqrt((k1.x - k2.x) ** 2 + (k1.y - k2.y) ** 2 + (k1.z - k2.z) ** 2)\n", "        frame_scores.append(frame_score / len(kp1))\n", "\n", "    return frame_scores\n", "\n", "# Calculate frame-by-frame similarity scores\n", "frame_scores = frame_similarity_scores(keypoints_video1, keypoints_video2)\n", "normalized_frame_scores = [1 / (1 + score) for score in frame_scores]\n", "\n", "# Plot frame-by-frame similarity scores\n", "plt.plot(normalized_frame_scores)\n", "plt.title('Frame-by-Frame Similarity Scores')\n", "plt.xlabel('Frame')\n", "plt.ylabel('Similarity Score')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "91d6a7ab-fd1f-4df7-918b-fc7d05fa32c8", "metadata": {}, "outputs": [], "source": ["# Function to combine and write video\n", "def combine_and_write_video(frames1, frames2, output_path):\n", "    height, width, _ = frames1[0].shape\n", "    combined_width = width * 2\n", "\n", "    fourcc = cv2.VideoWriter_fourcc(*'mp4v')\n", "    out = cv2.VideoWriter(output_path, fourcc, 30.0, (combined_width, height))\n", "\n", "    for frame1, frame2 in zip(frames1, frames2):\n", "        combined_frame = np.hstack((frame1, frame2))\n", "        out.write(combined_frame)\n", "\n", "    out.release()\n", "\n", "# Combine frames and write to new video\n", "combine_and_write_video(frames_video1, frames_video2, output_video_path)"]}, {"cell_type": "code", "execution_count": 11, "id": "aec090d6-a9e6-48e0-9916-8326706c6840", "metadata": {}, "outputs": [], "source": ["def generate_from_video(video_path, score):\n", "    vertexai.init(project=\"avid-shape-424005-p2\", location=\"us-central1\")\n", "    model = GenerativeModel(\n", "        \"gemini-1.5-flash-001\",\n", "    )\n", "\n", "    # Read the video file\n", "    with open(video_path, \"rb\") as video_file:\n", "        video_data = video_file.read()\n", "\n", "    video1 = Part.from_data(\n", "        mime_type=\"video/mp4\",\n", "        data=video_data\n", "    )\n", "\n", "    text = f\"\"\"Give a dictionary as response with 'Confidence Rating', 'Color Description', 'Funny Description', 'Commentary' as keys\n", "Confidence Rating:\n", "Rate the confidence of the person on the right out of 5 based on their posture and expression. Consider their body language, stance, and facial expressions.\n", "\n", "Color Description:\n", "Describe the color of the clothes worn by the person on the right.\n", "\n", "Funny Description:\n", "Give a humorous description of the person on the right, strictly tailor the description according to {score} which is the similarity score with respect to left person, Avoid using the words 'channeling,' 'inner,' 'emulate', and 'runway'\n", "\n", "Overall Commentary:\n", "Provide an overall commentary on the person on the right, including their confidence rating out of 5, the color of their clothes, and a funny description. Ensure the commentary is detailed and avoids the words 'channeling,' 'inner,' and 'runway.'''\"\"\"\n", "\n", "    generation_config = {\n", "        \"max_output_tokens\": 8192,\n", "        \"temperature\": 0.7,\n", "        \"top_p\": 0.95,\n", "    }\n", "\n", "    safety_settings = {\n", "        generative_models.HarmCategory.HARM_CATEGORY_HATE_SPEECH: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "        generative_models.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "        generative_models.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "        generative_models.HarmCategory.HARM_CATEGORY_HARASSMENT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,\n", "    }\n", "\n", "    responses = model.generate_content(\n", "        [video1, text],\n", "        generation_config=generation_config,\n", "        safety_settings=safety_settings,\n", "        stream=True,\n", "    )\n", "\n", "    for response in responses:\n", "        print(response.text, end=\"\")"]}, {"cell_type": "code", "execution_count": 12, "id": "9808e0f0-2440-4a03-87ed-f68688e44ee2", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/rampwalk/lib/python3.12/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"Confidence Rating\": \"3\",\n", "  \"Color Description\": \"The person is wearing a green T-shirt and blue jeans.\",\n", "  \"Funny Description\": \"He's rocking a look that's definitely not as sharp as the officer on the left, but hey, at least he's trying to keep it cool with those shades. \",\n", "  \"Commentary\": \"The person on the right seems a bit unsure of himself, giving him a confidence rating of 3 out of 5.  He's wearing a green T-shirt and blue jeans, a casual look that lacks the authority of the officer on the left.  He's sporting a pair of glasses and attempting to pull off a cool vibe, but something about his posture and expression just seems a bit off. It's almost as if he's trying to channel the officer's confidence, but it's not quite working out.\"\n", "}\n", "```None\n"]}], "source": ["# Example usage\n", "response_dict = generate_from_video('/home/<USER>/RampWalk/CombinedOutput.mp4', score=similarity_score)\n", "print(response_dict)"]}, {"cell_type": "code", "execution_count": null, "id": "d0deac55-8471-4e73-a69b-47282bb4ec00", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}