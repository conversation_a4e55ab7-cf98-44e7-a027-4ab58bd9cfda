# Copyright 2023 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Kernels used for testing pallas_call."""

from jax.experimental import pallas as pl


def double_kernel(x_ref, y_ref):
  y_ref[:] = x_ref[:] * 2


def double(x):
  return pl.pallas_call(double_kernel, out_shape=x)(x)
