# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/tensor/feedback_tensors_calculator.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/calculators/tensor/feedback_tensors_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xbb\x03\n FeedbackTensorsCalculatorOptions\x12V\n\x15\x66\x65\x65\x64\x62\x61\x63k_tensor_shape\x18\x01 \x01(\x0b\x32\x37.mediapipe.FeedbackTensorsCalculatorOptions.TensorShape\x12\x1f\n\x14num_feedback_tensors\x18\x02 \x01(\x05:\x01\x31\x12_\n\x08location\x18\x03 \x01(\x0e\x32\x43.mediapipe.FeedbackTensorsCalculatorOptions.FeedbackTensorsLocation:\x08\x41PPENDED\x1a\x1f\n\x0bTensorShape\x12\x10\n\x04\x64ims\x18\x01 \x03(\x05\x42\x02\x10\x01\"@\n\x17\x46\x65\x65\x64\x62\x61\x63kTensorsLocation\x12\x08\n\x04NONE\x10\x00\x12\r\n\tPREPENDED\x10\x01\x12\x0c\n\x08\x41PPENDED\x10\x02\x32Z\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xfc\xf9\xa0\xe2\x01 \x01(\x0b\x32+.mediapipe.FeedbackTensorsCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.tensor.feedback_tensors_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_TENSORSHAPE'].fields_by_name['dims']._options = None
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_TENSORSHAPE'].fields_by_name['dims']._serialized_options = b'\020\001'
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS']._serialized_start=116
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS']._serialized_end=559
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_TENSORSHAPE']._serialized_start=370
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_TENSORSHAPE']._serialized_end=401
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_FEEDBACKTENSORSLOCATION']._serialized_start=403
  _globals['_FEEDBACKTENSORSCALCULATOROPTIONS_FEEDBACKTENSORSLOCATION']._serialized_end=467
# @@protoc_insertion_point(module_scope)
