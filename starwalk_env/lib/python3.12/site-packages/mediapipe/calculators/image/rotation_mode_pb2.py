# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/image/rotation_mode.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/mediapipe/calculators/image/rotation_mode.proto\x12\tmediapipe\"h\n\x0cRotationMode\"X\n\x04Mode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0e\n\nROTATION_0\x10\x01\x12\x0f\n\x0bROTATION_90\x10\x02\x12\x10\n\x0cROTATION_180\x10\x03\x12\x10\n\x0cROTATION_270\x10\x04\x42:\n%com.google.mediapipe.calculator.protoB\x11RotationModeProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.image.rotation_mode_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.mediapipe.calculator.protoB\021RotationModeProto'
  _globals['_ROTATIONMODE']._serialized_start=62
  _globals['_ROTATIONMODE']._serialized_end=166
  _globals['_ROTATIONMODE_MODE']._serialized_start=78
  _globals['_ROTATIONMODE_MODE']._serialized_end=166
# @@protoc_insertion_point(module_scope)
