# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/push_pull_filtering.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1mediapipe/util/tracking/push_pull_filtering.proto\x12\tmediapipe\"\xc0\x01\n\x0fPushPullOptions\x12\x1b\n\x0f\x62ilateral_sigma\x18\x01 \x01(\x02:\x02\x32\x30\x12!\n\x16pull_propagation_scale\x18\x03 \x01(\x02:\x01\x38\x12!\n\x16push_propagation_scale\x18\x04 \x01(\x02:\x01\x38\x12!\n\x14pull_bilateral_scale\x18\x05 \x01(\x02:\x03\x30.7\x12!\n\x14push_bilateral_scale\x18\x06 \x01(\x02:\x03\x30.9*\x04\x08\x02\x10\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.push_pull_filtering_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_PUSHPULLOPTIONS']._serialized_start=65
  _globals['_PUSHPULLOPTIONS']._serialized_end=257
# @@protoc_insertion_point(module_scope)
