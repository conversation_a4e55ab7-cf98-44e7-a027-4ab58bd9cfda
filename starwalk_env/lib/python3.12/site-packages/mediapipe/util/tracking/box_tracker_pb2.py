# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/tracking/box_tracker.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.util.tracking import tracking_pb2 as mediapipe_dot_util_dot_tracking_dot_tracking__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)mediapipe/util/tracking/box_tracker.proto\x12\tmediapipe\x1a&mediapipe/util/tracking/tracking.proto\"\x86\x02\n\x11\x42oxTrackerOptions\x12%\n\x17\x63\x61\x63hing_chunk_size_msec\x18\x01 \x01(\x05:\x04\x32\x35\x30\x30\x12%\n\x11\x63\x61\x63he_file_format\x18\x02 \x01(\t:\nchunk_%04d\x12\x1f\n\x14num_tracking_workers\x18\x03 \x01(\x05:\x01\x38\x12&\n\x17read_chunk_timeout_msec\x18\x04 \x01(\x05:\x05\x36\x30\x30\x30\x30\x12!\n\x12record_path_states\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x37\n\x12track_step_options\x18\x06 \x01(\x0b\x32\x1b.mediapipe.TrackStepOptions\"\xa7\x02\n\rTimedBoxProto\x12\x0b\n\x03top\x18\x01 \x01(\x02\x12\x0c\n\x04left\x18\x02 \x01(\x02\x12\x0e\n\x06\x62ottom\x18\x03 \x01(\x02\x12\r\n\x05right\x18\x04 \x01(\x02\x12\x10\n\x08rotation\x18\x07 \x01(\x02\x12,\n\x04quad\x18\t \x01(\x0b\x32\x1e.mediapipe.MotionBoxState.Quad\x12\x14\n\ttime_msec\x18\x05 \x01(\x03:\x01\x30\x12\x0e\n\x02id\x18\x06 \x01(\x05:\x02-1\x12\r\n\x05label\x18\r \x01(\t\x12\x12\n\nconfidence\x18\x08 \x01(\x02\x12\x14\n\x0c\x61spect_ratio\x18\n \x01(\x02\x12\x1c\n\rreacquisition\x18\x0b \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10request_grouping\x18\x0c \x01(\x08:\x05\x66\x61lse\":\n\x11TimedBoxProtoList\x12%\n\x03\x62ox\x18\x01 \x03(\x0b\x32\x18.mediapipe.TimedBoxProtoB0\n\x1d\x63om.google.mediapipe.trackingB\x0f\x42oxTrackerProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.tracking.box_tracker_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.google.mediapipe.trackingB\017BoxTrackerProto'
  _globals['_BOXTRACKEROPTIONS']._serialized_start=97
  _globals['_BOXTRACKEROPTIONS']._serialized_end=359
  _globals['_TIMEDBOXPROTO']._serialized_start=362
  _globals['_TIMEDBOXPROTO']._serialized_end=657
  _globals['_TIMEDBOXPROTOLIST']._serialized_start=659
  _globals['_TIMEDBOXPROTOLIST']._serialized_end=717
# @@protoc_insertion_point(module_scope)
