# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_roi_refinement_graph_options.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.tasks.cc.core.proto import base_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_core_dot_proto_dot_base__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nWmediapipe/tasks/cc/vision/hand_landmarker/proto/hand_roi_refinement_graph_options.proto\x12,mediapipe.tasks.vision.hand_landmarker.proto\x1a\x30mediapipe/tasks/cc/core/proto/base_options.proto\"^\n\x1dHandRoiRefinementGraphOptions\x12=\n\x0c\x62\x61se_options\x18\x01 \x01(\x0b\x32\'.mediapipe.tasks.core.proto.BaseOptionsB\\\n6com.google.mediapipe.tasks.vision.handlandmarker.protoB\"HandRoiRefinementGraphOptionsProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.hand_landmarker.proto.hand_roi_refinement_graph_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n6com.google.mediapipe.tasks.vision.handlandmarker.protoB\"HandRoiRefinementGraphOptionsProto'
  _globals['_HANDROIREFINEMENTGRAPHOPTIONS']._serialized_start=187
  _globals['_HANDROIREFINEMENTGRAPHOPTIONS']._serialized_end=281
# @@protoc_insertion_point(module_scope)
