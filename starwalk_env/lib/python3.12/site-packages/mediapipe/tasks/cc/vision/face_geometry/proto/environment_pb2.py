# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/vision/face_geometry/proto/environment.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/tasks/cc/vision/face_geometry/proto/environment.proto\x12*mediapipe.tasks.vision.face_geometry.proto\"L\n\x11PerspectiveCamera\x12\x1c\n\x14vertical_fov_degrees\x18\x01 \x01(\x02\x12\x0c\n\x04near\x18\x02 \x01(\x02\x12\x0b\n\x03\x66\x61r\x18\x03 \x01(\x02\"\xc8\x01\n\x0b\x45nvironment\x12^\n\x15origin_point_location\x18\x01 \x01(\x0e\x32?.mediapipe.tasks.vision.face_geometry.proto.OriginPointLocation\x12Y\n\x12perspective_camera\x18\x02 \x01(\x0b\x32=.mediapipe.tasks.vision.face_geometry.proto.PerspectiveCamera*B\n\x13OriginPointLocation\x12\x16\n\x12\x42OTTOM_LEFT_CORNER\x10\x01\x12\x13\n\x0fTOP_LEFT_CORNER\x10\x02\x42H\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\x10\x45nvironmentProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.vision.face_geometry.proto.environment_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n4com.google.mediapipe.tasks.vision.facegeometry.protoB\020EnvironmentProto'
  _globals['_ORIGINPOINTLOCATION']._serialized_start=392
  _globals['_ORIGINPOINTLOCATION']._serialized_end=458
  _globals['_PERSPECTIVECAMERA']._serialized_start=111
  _globals['_PERSPECTIVECAMERA']._serialized_end=187
  _globals['_ENVIRONMENT']._serialized_start=190
  _globals['_ENVIRONMENT']._serialized_end=390
# @@protoc_insertion_point(module_scope)
