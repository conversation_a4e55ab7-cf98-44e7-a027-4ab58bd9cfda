# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/genai/inference/proto/sampler_params.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=mediapipe/tasks/cc/genai/inference/proto/sampler_params.proto\x12\x10odml.infra.proto\"\xd2\x01\n\x11SamplerParameters\x12\x36\n\x04type\x18\x01 \x01(\x0e\x32(.odml.infra.proto.SamplerParameters.Type\x12\t\n\x01k\x18\x02 \x01(\x05\x12\t\n\x01p\x18\x03 \x01(\x02\x12\x13\n\x0btemperature\x18\x04 \x01(\x02\x12\x11\n\x04seed\x18\x05 \x01(\x05H\x00\x88\x01\x01\">\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05TOP_K\x10\x01\x12\t\n\x05TOP_P\x10\x02\x12\n\n\x06GREEDY\x10\x03\x42\x07\n\x05_seedB5\n\x1b\x63om.google.odml.infra.protoB\x16SamplerParametersProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.genai.inference.proto.sampler_params_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033com.google.odml.infra.protoB\026SamplerParametersProto'
  _globals['_SAMPLERPARAMETERS']._serialized_start=84
  _globals['_SAMPLERPARAMETERS']._serialized_end=294
  _globals['_SAMPLERPARAMETERS_TYPE']._serialized_start=223
  _globals['_SAMPLERPARAMETERS_TYPE']._serialized_end=285
# @@protoc_insertion_point(module_scope)
