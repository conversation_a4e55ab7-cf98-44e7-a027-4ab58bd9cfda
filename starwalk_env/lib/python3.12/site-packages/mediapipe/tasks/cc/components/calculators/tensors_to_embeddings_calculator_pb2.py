# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/calculators/tensors_to_embeddings_calculator.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2
from mediapipe.tasks.cc.components.processors.proto import embedder_options_pb2 as mediapipe_dot_tasks_dot_cc_dot_components_dot_processors_dot_proto_dot_embedder__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nPmediapipe/tasks/cc/components/calculators/tensors_to_embeddings_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\x1a\x45mediapipe/tasks/cc/components/processors/proto/embedder_options.proto\"\x8e\x02\n$TensorsToEmbeddingsCalculatorOptions\x12V\n\x10\x65mbedder_options\x18\x01 \x01(\x0b\x32<.mediapipe.tasks.components.processors.proto.EmbedderOptions\x12\x12\n\nhead_names\x18\x02 \x03(\t\x12\x1a\n\x12ignored_head_names\x18\x03 \x03(\t2^\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xd6\x98\xb1\xe2\x01 \x01(\x0b\x32/.mediapipe.TensorsToEmbeddingsCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.calculators.tensors_to_embeddings_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_TENSORSTOEMBEDDINGSCALCULATOROPTIONS']._serialized_start=205
  _globals['_TENSORSTOEMBEDDINGSCALCULATOROPTIONS']._serialized_end=475
# @@protoc_insertion_point(module_scope)
