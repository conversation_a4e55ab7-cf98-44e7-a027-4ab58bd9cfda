# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/components/processors/proto/classifier_options.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nGmediapipe/tasks/cc/components/processors/proto/classifier_options.proto\x12+mediapipe.tasks.components.processors.proto\"\x9e\x01\n\x11\x43lassifierOptions\x12 \n\x14\x64isplay_names_locale\x18\x01 \x01(\t:\x02\x65n\x12\x17\n\x0bmax_results\x18\x02 \x01(\x05:\x02-1\x12\x17\n\x0fscore_threshold\x18\x03 \x01(\x02\x12\x1a\n\x12\x63\x61tegory_allowlist\x18\x04 \x03(\t\x12\x19\n\x11\x63\x61tegory_denylist\x18\x05 \x03(\tBP\n6com.google.mediapipe.tasks.components.processors.protoB\x16\x43lassifierOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.components.processors.proto.classifier_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n6com.google.mediapipe.tasks.components.processors.protoB\026ClassifierOptionsProto'
  _globals['_CLASSIFIEROPTIONS']._serialized_start=121
  _globals['_CLASSIFIEROPTIONS']._serialized_end=279
# @@protoc_insertion_point(module_scope)
