import flatbuffers

# automatically generated by the FlatBuffers compiler, do not modify

# namespace: tflite

from flatbuffers.compat import import_numpy
np = import_numpy()

class AssociatedFileType(object):
    UNKNOWN = 0
    DESCRIPTIONS = 1
    TENSOR_AXIS_LABELS = 2
    TENSOR_VALUE_LABELS = 3
    TENSOR_AXIS_SCORE_CALIBRATION = 4
    VOCABULARY = 5
    SCANN_INDEX_FILE = 6


class ColorSpaceType(object):
    UNKNOWN = 0
    RGB = 1
    GRAYSCALE = 2


class BoundingBoxType(object):
    UNKNOWN = 0
    BOUNDARIES = 1
    UPPER_LEFT = 2
    CENTER = 3


class CoordinateType(object):
    RATIO = 0
    PIXEL = 1


class ContentProperties(object):
    NONE = 0
    FeatureProperties = 1
    ImageProperties = 2
    BoundingBoxProperties = 3
    AudioProperties = 4

def ContentPropertiesCreator(unionType, table):
    from flatbuffers.table import Table
    if not isinstance(table, Table):
        return None
    if unionType == ContentProperties().FeatureProperties:
        return FeaturePropertiesT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ContentProperties().ImageProperties:
        return ImagePropertiesT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ContentProperties().BoundingBoxProperties:
        return BoundingBoxPropertiesT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ContentProperties().AudioProperties:
        return AudioPropertiesT.InitFromBuf(table.Bytes, table.Pos)
    return None


class ScoreTransformationType(object):
    IDENTITY = 0
    LOG = 1
    INVERSE_LOGISTIC = 2


class ProcessUnitOptions(object):
    NONE = 0
    NormalizationOptions = 1
    ScoreCalibrationOptions = 2
    ScoreThresholdingOptions = 3
    BertTokenizerOptions = 4
    SentencePieceTokenizerOptions = 5
    RegexTokenizerOptions = 6

def ProcessUnitOptionsCreator(unionType, table):
    from flatbuffers.table import Table
    if not isinstance(table, Table):
        return None
    if unionType == ProcessUnitOptions().NormalizationOptions:
        return NormalizationOptionsT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ProcessUnitOptions().ScoreCalibrationOptions:
        return ScoreCalibrationOptionsT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ProcessUnitOptions().ScoreThresholdingOptions:
        return ScoreThresholdingOptionsT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ProcessUnitOptions().BertTokenizerOptions:
        return BertTokenizerOptionsT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ProcessUnitOptions().SentencePieceTokenizerOptions:
        return SentencePieceTokenizerOptionsT.InitFromBuf(table.Bytes, table.Pos)
    if unionType == ProcessUnitOptions().RegexTokenizerOptions:
        return RegexTokenizerOptionsT.InitFromBuf(table.Bytes, table.Pos)
    return None


class AssociatedFile(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AssociatedFile()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAssociatedFile(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def AssociatedFileBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # AssociatedFile
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AssociatedFile
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # AssociatedFile
    def Description(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # AssociatedFile
    def Type(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

    # AssociatedFile
    def Locale(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # AssociatedFile
    def Version(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def AssociatedFileStart(builder):
    builder.StartObject(5)

def AssociatedFileAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def AssociatedFileAddDescription(builder, description):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(description), 0)

def AssociatedFileAddType(builder, type):
    builder.PrependInt8Slot(2, type, 0)

def AssociatedFileAddLocale(builder, locale):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(locale), 0)

def AssociatedFileAddVersion(builder, version):
    builder.PrependUOffsetTRelativeSlot(4, flatbuffers.number_types.UOffsetTFlags.py_type(version), 0)

def AssociatedFileEnd(builder):
    return builder.EndObject()



class AssociatedFileT(object):

    # AssociatedFileT
    def __init__(self):
        self.name = None  # type: str
        self.description = None  # type: str
        self.type = 0  # type: int
        self.locale = None  # type: str
        self.version = None  # type: str

    @classmethod
    def InitFromBuf(cls, buf, pos):
        associatedFile = AssociatedFile()
        associatedFile.Init(buf, pos)
        return cls.InitFromObj(associatedFile)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, associatedFile):
        x = AssociatedFileT()
        x._UnPack(associatedFile)
        return x

    # AssociatedFileT
    def _UnPack(self, associatedFile):
        if associatedFile is None:
            return
        self.name = associatedFile.Name()
        self.description = associatedFile.Description()
        self.type = associatedFile.Type()
        self.locale = associatedFile.Locale()
        self.version = associatedFile.Version()

    # AssociatedFileT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.description is not None:
            description = builder.CreateString(self.description)
        if self.locale is not None:
            locale = builder.CreateString(self.locale)
        if self.version is not None:
            version = builder.CreateString(self.version)
        AssociatedFileStart(builder)
        if self.name is not None:
            AssociatedFileAddName(builder, name)
        if self.description is not None:
            AssociatedFileAddDescription(builder, description)
        AssociatedFileAddType(builder, self.type)
        if self.locale is not None:
            AssociatedFileAddLocale(builder, locale)
        if self.version is not None:
            AssociatedFileAddVersion(builder, version)
        associatedFile = AssociatedFileEnd(builder)
        return associatedFile


class FeatureProperties(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = FeatureProperties()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsFeatureProperties(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def FeaturePropertiesBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # FeatureProperties
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def FeaturePropertiesStart(builder):
    builder.StartObject(0)

def FeaturePropertiesEnd(builder):
    return builder.EndObject()



class FeaturePropertiesT(object):

    # FeaturePropertiesT
    def __init__(self):
        pass

    @classmethod
    def InitFromBuf(cls, buf, pos):
        featureProperties = FeatureProperties()
        featureProperties.Init(buf, pos)
        return cls.InitFromObj(featureProperties)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, featureProperties):
        x = FeaturePropertiesT()
        x._UnPack(featureProperties)
        return x

    # FeaturePropertiesT
    def _UnPack(self, featureProperties):
        if featureProperties is None:
            return

    # FeaturePropertiesT
    def Pack(self, builder):
        FeaturePropertiesStart(builder)
        featureProperties = FeaturePropertiesEnd(builder)
        return featureProperties


class ImageSize(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ImageSize()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsImageSize(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ImageSizeBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ImageSize
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ImageSize
    def Width(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # ImageSize
    def Height(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

def ImageSizeStart(builder):
    builder.StartObject(2)

def ImageSizeAddWidth(builder, width):
    builder.PrependUint32Slot(0, width, 0)

def ImageSizeAddHeight(builder, height):
    builder.PrependUint32Slot(1, height, 0)

def ImageSizeEnd(builder):
    return builder.EndObject()



class ImageSizeT(object):

    # ImageSizeT
    def __init__(self):
        self.width = 0  # type: int
        self.height = 0  # type: int

    @classmethod
    def InitFromBuf(cls, buf, pos):
        imageSize = ImageSize()
        imageSize.Init(buf, pos)
        return cls.InitFromObj(imageSize)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, imageSize):
        x = ImageSizeT()
        x._UnPack(imageSize)
        return x

    # ImageSizeT
    def _UnPack(self, imageSize):
        if imageSize is None:
            return
        self.width = imageSize.Width()
        self.height = imageSize.Height()

    # ImageSizeT
    def Pack(self, builder):
        ImageSizeStart(builder)
        ImageSizeAddWidth(builder, self.width)
        ImageSizeAddHeight(builder, self.height)
        imageSize = ImageSizeEnd(builder)
        return imageSize


class ImageProperties(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ImageProperties()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsImageProperties(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ImagePropertiesBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ImageProperties
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ImageProperties
    def ColorSpace(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

    # ImageProperties
    def DefaultSize(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = ImageSize()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def ImagePropertiesStart(builder):
    builder.StartObject(2)

def ImagePropertiesAddColorSpace(builder, colorSpace):
    builder.PrependInt8Slot(0, colorSpace, 0)

def ImagePropertiesAddDefaultSize(builder, defaultSize):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(defaultSize), 0)

def ImagePropertiesEnd(builder):
    return builder.EndObject()


try:
    from typing import Optional
except:
    pass

class ImagePropertiesT(object):

    # ImagePropertiesT
    def __init__(self):
        self.colorSpace = 0  # type: int
        self.defaultSize = None  # type: Optional[ImageSizeT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        imageProperties = ImageProperties()
        imageProperties.Init(buf, pos)
        return cls.InitFromObj(imageProperties)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, imageProperties):
        x = ImagePropertiesT()
        x._UnPack(imageProperties)
        return x

    # ImagePropertiesT
    def _UnPack(self, imageProperties):
        if imageProperties is None:
            return
        self.colorSpace = imageProperties.ColorSpace()
        if imageProperties.DefaultSize() is not None:
            self.defaultSize = ImageSizeT.InitFromObj(imageProperties.DefaultSize())

    # ImagePropertiesT
    def Pack(self, builder):
        if self.defaultSize is not None:
            defaultSize = self.defaultSize.Pack(builder)
        ImagePropertiesStart(builder)
        ImagePropertiesAddColorSpace(builder, self.colorSpace)
        if self.defaultSize is not None:
            ImagePropertiesAddDefaultSize(builder, defaultSize)
        imageProperties = ImagePropertiesEnd(builder)
        return imageProperties


class AudioProperties(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AudioProperties()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAudioProperties(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def AudioPropertiesBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # AudioProperties
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AudioProperties
    def SampleRate(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # AudioProperties
    def Channels(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

def AudioPropertiesStart(builder):
    builder.StartObject(2)

def AudioPropertiesAddSampleRate(builder, sampleRate):
    builder.PrependUint32Slot(0, sampleRate, 0)

def AudioPropertiesAddChannels(builder, channels):
    builder.PrependUint32Slot(1, channels, 0)

def AudioPropertiesEnd(builder):
    return builder.EndObject()



class AudioPropertiesT(object):

    # AudioPropertiesT
    def __init__(self):
        self.sampleRate = 0  # type: int
        self.channels = 0  # type: int

    @classmethod
    def InitFromBuf(cls, buf, pos):
        audioProperties = AudioProperties()
        audioProperties.Init(buf, pos)
        return cls.InitFromObj(audioProperties)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, audioProperties):
        x = AudioPropertiesT()
        x._UnPack(audioProperties)
        return x

    # AudioPropertiesT
    def _UnPack(self, audioProperties):
        if audioProperties is None:
            return
        self.sampleRate = audioProperties.SampleRate()
        self.channels = audioProperties.Channels()

    # AudioPropertiesT
    def Pack(self, builder):
        AudioPropertiesStart(builder)
        AudioPropertiesAddSampleRate(builder, self.sampleRate)
        AudioPropertiesAddChannels(builder, self.channels)
        audioProperties = AudioPropertiesEnd(builder)
        return audioProperties


class BoundingBoxProperties(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = BoundingBoxProperties()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsBoundingBoxProperties(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def BoundingBoxPropertiesBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # BoundingBoxProperties
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # BoundingBoxProperties
    def Index(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # BoundingBoxProperties
    def IndexAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Uint32Flags, o)
        return 0

    # BoundingBoxProperties
    def IndexLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # BoundingBoxProperties
    def IndexIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # BoundingBoxProperties
    def Type(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

    # BoundingBoxProperties
    def CoordinateType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

def BoundingBoxPropertiesStart(builder):
    builder.StartObject(3)

def BoundingBoxPropertiesAddIndex(builder, index):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(index), 0)

def BoundingBoxPropertiesStartIndexVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def BoundingBoxPropertiesAddType(builder, type):
    builder.PrependInt8Slot(1, type, 0)

def BoundingBoxPropertiesAddCoordinateType(builder, coordinateType):
    builder.PrependInt8Slot(2, coordinateType, 0)

def BoundingBoxPropertiesEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class BoundingBoxPropertiesT(object):

    # BoundingBoxPropertiesT
    def __init__(self):
        self.index = None  # type: List[int]
        self.type = 0  # type: int
        self.coordinateType = 0  # type: int

    @classmethod
    def InitFromBuf(cls, buf, pos):
        boundingBoxProperties = BoundingBoxProperties()
        boundingBoxProperties.Init(buf, pos)
        return cls.InitFromObj(boundingBoxProperties)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, boundingBoxProperties):
        x = BoundingBoxPropertiesT()
        x._UnPack(boundingBoxProperties)
        return x

    # BoundingBoxPropertiesT
    def _UnPack(self, boundingBoxProperties):
        if boundingBoxProperties is None:
            return
        if not boundingBoxProperties.IndexIsNone():
            if np is None:
                self.index = []
                for i in range(boundingBoxProperties.IndexLength()):
                    self.index.append(boundingBoxProperties.Index(i))
            else:
                self.index = boundingBoxProperties.IndexAsNumpy()
        self.type = boundingBoxProperties.Type()
        self.coordinateType = boundingBoxProperties.CoordinateType()

    # BoundingBoxPropertiesT
    def Pack(self, builder):
        if self.index is not None:
            if np is not None and type(self.index) is np.ndarray:
                index = builder.CreateNumpyVector(self.index)
            else:
                BoundingBoxPropertiesStartIndexVector(builder, len(self.index))
                for i in reversed(range(len(self.index))):
                    builder.PrependUint32(self.index[i])
                index = builder.EndVector()
        BoundingBoxPropertiesStart(builder)
        if self.index is not None:
            BoundingBoxPropertiesAddIndex(builder, index)
        BoundingBoxPropertiesAddType(builder, self.type)
        BoundingBoxPropertiesAddCoordinateType(builder, self.coordinateType)
        boundingBoxProperties = BoundingBoxPropertiesEnd(builder)
        return boundingBoxProperties


class ValueRange(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ValueRange()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsValueRange(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ValueRangeBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ValueRange
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ValueRange
    def Min(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # ValueRange
    def Max(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

def ValueRangeStart(builder):
    builder.StartObject(2)

def ValueRangeAddMin(builder, min):
    builder.PrependInt32Slot(0, min, 0)

def ValueRangeAddMax(builder, max):
    builder.PrependInt32Slot(1, max, 0)

def ValueRangeEnd(builder):
    return builder.EndObject()



class ValueRangeT(object):

    # ValueRangeT
    def __init__(self):
        self.min = 0  # type: int
        self.max = 0  # type: int

    @classmethod
    def InitFromBuf(cls, buf, pos):
        valueRange = ValueRange()
        valueRange.Init(buf, pos)
        return cls.InitFromObj(valueRange)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, valueRange):
        x = ValueRangeT()
        x._UnPack(valueRange)
        return x

    # ValueRangeT
    def _UnPack(self, valueRange):
        if valueRange is None:
            return
        self.min = valueRange.Min()
        self.max = valueRange.Max()

    # ValueRangeT
    def Pack(self, builder):
        ValueRangeStart(builder)
        ValueRangeAddMin(builder, self.min)
        ValueRangeAddMax(builder, self.max)
        valueRange = ValueRangeEnd(builder)
        return valueRange


class Content(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Content()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsContent(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ContentBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # Content
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Content
    def ContentPropertiesType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # Content
    def ContentProperties(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            from flatbuffers.table import Table
            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None

    # Content
    def Range(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = ValueRange()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def ContentStart(builder):
    builder.StartObject(3)

def ContentAddContentPropertiesType(builder, contentPropertiesType):
    builder.PrependUint8Slot(0, contentPropertiesType, 0)

def ContentAddContentProperties(builder, contentProperties):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(contentProperties), 0)

def ContentAddRange(builder, range):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(range), 0)

def ContentEnd(builder):
    return builder.EndObject()


try:
    from typing import Optional, Union
except:
    pass

class ContentT(object):

    # ContentT
    def __init__(self):
        self.contentPropertiesType = 0  # type: int
        self.contentProperties = None  # type: Union[None, FeaturePropertiesT, ImagePropertiesT, BoundingBoxPropertiesT, AudioPropertiesT]
        self.range = None  # type: Optional[ValueRangeT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        content = Content()
        content.Init(buf, pos)
        return cls.InitFromObj(content)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, content):
        x = ContentT()
        x._UnPack(content)
        return x

    # ContentT
    def _UnPack(self, content):
        if content is None:
            return
        self.contentPropertiesType = content.ContentPropertiesType()
        self.contentProperties = ContentPropertiesCreator(self.contentPropertiesType, content.ContentProperties())
        if content.Range() is not None:
            self.range = ValueRangeT.InitFromObj(content.Range())

    # ContentT
    def Pack(self, builder):
        if self.contentProperties is not None:
            contentProperties = self.contentProperties.Pack(builder)
        if self.range is not None:
            range = self.range.Pack(builder)
        ContentStart(builder)
        ContentAddContentPropertiesType(builder, self.contentPropertiesType)
        if self.contentProperties is not None:
            ContentAddContentProperties(builder, contentProperties)
        if self.range is not None:
            ContentAddRange(builder, range)
        content = ContentEnd(builder)
        return content


class NormalizationOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = NormalizationOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsNormalizationOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def NormalizationOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # NormalizationOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # NormalizationOptions
    def Mean(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Float32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # NormalizationOptions
    def MeanAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Float32Flags, o)
        return 0

    # NormalizationOptions
    def MeanLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # NormalizationOptions
    def MeanIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # NormalizationOptions
    def Std(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Float32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # NormalizationOptions
    def StdAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Float32Flags, o)
        return 0

    # NormalizationOptions
    def StdLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # NormalizationOptions
    def StdIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def NormalizationOptionsStart(builder):
    builder.StartObject(2)

def NormalizationOptionsAddMean(builder, mean):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(mean), 0)

def NormalizationOptionsStartMeanVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def NormalizationOptionsAddStd(builder, std):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(std), 0)

def NormalizationOptionsStartStdVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def NormalizationOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class NormalizationOptionsT(object):

    # NormalizationOptionsT
    def __init__(self):
        self.mean = None  # type: List[float]
        self.std = None  # type: List[float]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        normalizationOptions = NormalizationOptions()
        normalizationOptions.Init(buf, pos)
        return cls.InitFromObj(normalizationOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, normalizationOptions):
        x = NormalizationOptionsT()
        x._UnPack(normalizationOptions)
        return x

    # NormalizationOptionsT
    def _UnPack(self, normalizationOptions):
        if normalizationOptions is None:
            return
        if not normalizationOptions.MeanIsNone():
            if np is None:
                self.mean = []
                for i in range(normalizationOptions.MeanLength()):
                    self.mean.append(normalizationOptions.Mean(i))
            else:
                self.mean = normalizationOptions.MeanAsNumpy()
        if not normalizationOptions.StdIsNone():
            if np is None:
                self.std = []
                for i in range(normalizationOptions.StdLength()):
                    self.std.append(normalizationOptions.Std(i))
            else:
                self.std = normalizationOptions.StdAsNumpy()

    # NormalizationOptionsT
    def Pack(self, builder):
        if self.mean is not None:
            if np is not None and type(self.mean) is np.ndarray:
                mean = builder.CreateNumpyVector(self.mean)
            else:
                NormalizationOptionsStartMeanVector(builder, len(self.mean))
                for i in reversed(range(len(self.mean))):
                    builder.PrependFloat32(self.mean[i])
                mean = builder.EndVector()
        if self.std is not None:
            if np is not None and type(self.std) is np.ndarray:
                std = builder.CreateNumpyVector(self.std)
            else:
                NormalizationOptionsStartStdVector(builder, len(self.std))
                for i in reversed(range(len(self.std))):
                    builder.PrependFloat32(self.std[i])
                std = builder.EndVector()
        NormalizationOptionsStart(builder)
        if self.mean is not None:
            NormalizationOptionsAddMean(builder, mean)
        if self.std is not None:
            NormalizationOptionsAddStd(builder, std)
        normalizationOptions = NormalizationOptionsEnd(builder)
        return normalizationOptions


class ScoreCalibrationOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ScoreCalibrationOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsScoreCalibrationOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ScoreCalibrationOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ScoreCalibrationOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ScoreCalibrationOptions
    def ScoreTransformation(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int8Flags, o + self._tab.Pos)
        return 0

    # ScoreCalibrationOptions
    def DefaultScore(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

def ScoreCalibrationOptionsStart(builder):
    builder.StartObject(2)

def ScoreCalibrationOptionsAddScoreTransformation(builder, scoreTransformation):
    builder.PrependInt8Slot(0, scoreTransformation, 0)

def ScoreCalibrationOptionsAddDefaultScore(builder, defaultScore):
    builder.PrependFloat32Slot(1, defaultScore, 0.0)

def ScoreCalibrationOptionsEnd(builder):
    return builder.EndObject()



class ScoreCalibrationOptionsT(object):

    # ScoreCalibrationOptionsT
    def __init__(self):
        self.scoreTransformation = 0  # type: int
        self.defaultScore = 0.0  # type: float

    @classmethod
    def InitFromBuf(cls, buf, pos):
        scoreCalibrationOptions = ScoreCalibrationOptions()
        scoreCalibrationOptions.Init(buf, pos)
        return cls.InitFromObj(scoreCalibrationOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, scoreCalibrationOptions):
        x = ScoreCalibrationOptionsT()
        x._UnPack(scoreCalibrationOptions)
        return x

    # ScoreCalibrationOptionsT
    def _UnPack(self, scoreCalibrationOptions):
        if scoreCalibrationOptions is None:
            return
        self.scoreTransformation = scoreCalibrationOptions.ScoreTransformation()
        self.defaultScore = scoreCalibrationOptions.DefaultScore()

    # ScoreCalibrationOptionsT
    def Pack(self, builder):
        ScoreCalibrationOptionsStart(builder)
        ScoreCalibrationOptionsAddScoreTransformation(builder, self.scoreTransformation)
        ScoreCalibrationOptionsAddDefaultScore(builder, self.defaultScore)
        scoreCalibrationOptions = ScoreCalibrationOptionsEnd(builder)
        return scoreCalibrationOptions


class ScoreThresholdingOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ScoreThresholdingOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsScoreThresholdingOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ScoreThresholdingOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ScoreThresholdingOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ScoreThresholdingOptions
    def GlobalScoreThreshold(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

def ScoreThresholdingOptionsStart(builder):
    builder.StartObject(1)

def ScoreThresholdingOptionsAddGlobalScoreThreshold(builder, globalScoreThreshold):
    builder.PrependFloat32Slot(0, globalScoreThreshold, 0.0)

def ScoreThresholdingOptionsEnd(builder):
    return builder.EndObject()



class ScoreThresholdingOptionsT(object):

    # ScoreThresholdingOptionsT
    def __init__(self):
        self.globalScoreThreshold = 0.0  # type: float

    @classmethod
    def InitFromBuf(cls, buf, pos):
        scoreThresholdingOptions = ScoreThresholdingOptions()
        scoreThresholdingOptions.Init(buf, pos)
        return cls.InitFromObj(scoreThresholdingOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, scoreThresholdingOptions):
        x = ScoreThresholdingOptionsT()
        x._UnPack(scoreThresholdingOptions)
        return x

    # ScoreThresholdingOptionsT
    def _UnPack(self, scoreThresholdingOptions):
        if scoreThresholdingOptions is None:
            return
        self.globalScoreThreshold = scoreThresholdingOptions.GlobalScoreThreshold()

    # ScoreThresholdingOptionsT
    def Pack(self, builder):
        ScoreThresholdingOptionsStart(builder)
        ScoreThresholdingOptionsAddGlobalScoreThreshold(builder, self.globalScoreThreshold)
        scoreThresholdingOptions = ScoreThresholdingOptionsEnd(builder)
        return scoreThresholdingOptions


class BertTokenizerOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = BertTokenizerOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsBertTokenizerOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def BertTokenizerOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # BertTokenizerOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # BertTokenizerOptions
    def VocabFile(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # BertTokenizerOptions
    def VocabFileLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # BertTokenizerOptions
    def VocabFileIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

def BertTokenizerOptionsStart(builder):
    builder.StartObject(1)

def BertTokenizerOptionsAddVocabFile(builder, vocabFile):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(vocabFile), 0)

def BertTokenizerOptionsStartVocabFileVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def BertTokenizerOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class BertTokenizerOptionsT(object):

    # BertTokenizerOptionsT
    def __init__(self):
        self.vocabFile = None  # type: List[AssociatedFileT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        bertTokenizerOptions = BertTokenizerOptions()
        bertTokenizerOptions.Init(buf, pos)
        return cls.InitFromObj(bertTokenizerOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, bertTokenizerOptions):
        x = BertTokenizerOptionsT()
        x._UnPack(bertTokenizerOptions)
        return x

    # BertTokenizerOptionsT
    def _UnPack(self, bertTokenizerOptions):
        if bertTokenizerOptions is None:
            return
        if not bertTokenizerOptions.VocabFileIsNone():
            self.vocabFile = []
            for i in range(bertTokenizerOptions.VocabFileLength()):
                if bertTokenizerOptions.VocabFile(i) is None:
                    self.vocabFile.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(bertTokenizerOptions.VocabFile(i))
                    self.vocabFile.append(associatedFile_)

    # BertTokenizerOptionsT
    def Pack(self, builder):
        if self.vocabFile is not None:
            vocabFilelist = []
            for i in range(len(self.vocabFile)):
                vocabFilelist.append(self.vocabFile[i].Pack(builder))
            BertTokenizerOptionsStartVocabFileVector(builder, len(self.vocabFile))
            for i in reversed(range(len(self.vocabFile))):
                builder.PrependUOffsetTRelative(vocabFilelist[i])
            vocabFile = builder.EndVector()
        BertTokenizerOptionsStart(builder)
        if self.vocabFile is not None:
            BertTokenizerOptionsAddVocabFile(builder, vocabFile)
        bertTokenizerOptions = BertTokenizerOptionsEnd(builder)
        return bertTokenizerOptions


class SentencePieceTokenizerOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = SentencePieceTokenizerOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSentencePieceTokenizerOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def SentencePieceTokenizerOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # SentencePieceTokenizerOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # SentencePieceTokenizerOptions
    def SentencePieceModel(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SentencePieceTokenizerOptions
    def SentencePieceModelLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SentencePieceTokenizerOptions
    def SentencePieceModelIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # SentencePieceTokenizerOptions
    def VocabFile(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SentencePieceTokenizerOptions
    def VocabFileLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SentencePieceTokenizerOptions
    def VocabFileIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def SentencePieceTokenizerOptionsStart(builder):
    builder.StartObject(2)

def SentencePieceTokenizerOptionsAddSentencePieceModel(builder, sentencePieceModel):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(sentencePieceModel), 0)

def SentencePieceTokenizerOptionsStartSentencePieceModelVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SentencePieceTokenizerOptionsAddVocabFile(builder, vocabFile):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(vocabFile), 0)

def SentencePieceTokenizerOptionsStartVocabFileVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SentencePieceTokenizerOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class SentencePieceTokenizerOptionsT(object):

    # SentencePieceTokenizerOptionsT
    def __init__(self):
        self.sentencePieceModel = None  # type: List[AssociatedFileT]
        self.vocabFile = None  # type: List[AssociatedFileT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        sentencePieceTokenizerOptions = SentencePieceTokenizerOptions()
        sentencePieceTokenizerOptions.Init(buf, pos)
        return cls.InitFromObj(sentencePieceTokenizerOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, sentencePieceTokenizerOptions):
        x = SentencePieceTokenizerOptionsT()
        x._UnPack(sentencePieceTokenizerOptions)
        return x

    # SentencePieceTokenizerOptionsT
    def _UnPack(self, sentencePieceTokenizerOptions):
        if sentencePieceTokenizerOptions is None:
            return
        if not sentencePieceTokenizerOptions.SentencePieceModelIsNone():
            self.sentencePieceModel = []
            for i in range(sentencePieceTokenizerOptions.SentencePieceModelLength()):
                if sentencePieceTokenizerOptions.SentencePieceModel(i) is None:
                    self.sentencePieceModel.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(sentencePieceTokenizerOptions.SentencePieceModel(i))
                    self.sentencePieceModel.append(associatedFile_)
        if not sentencePieceTokenizerOptions.VocabFileIsNone():
            self.vocabFile = []
            for i in range(sentencePieceTokenizerOptions.VocabFileLength()):
                if sentencePieceTokenizerOptions.VocabFile(i) is None:
                    self.vocabFile.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(sentencePieceTokenizerOptions.VocabFile(i))
                    self.vocabFile.append(associatedFile_)

    # SentencePieceTokenizerOptionsT
    def Pack(self, builder):
        if self.sentencePieceModel is not None:
            sentencePieceModellist = []
            for i in range(len(self.sentencePieceModel)):
                sentencePieceModellist.append(self.sentencePieceModel[i].Pack(builder))
            SentencePieceTokenizerOptionsStartSentencePieceModelVector(builder, len(self.sentencePieceModel))
            for i in reversed(range(len(self.sentencePieceModel))):
                builder.PrependUOffsetTRelative(sentencePieceModellist[i])
            sentencePieceModel = builder.EndVector()
        if self.vocabFile is not None:
            vocabFilelist = []
            for i in range(len(self.vocabFile)):
                vocabFilelist.append(self.vocabFile[i].Pack(builder))
            SentencePieceTokenizerOptionsStartVocabFileVector(builder, len(self.vocabFile))
            for i in reversed(range(len(self.vocabFile))):
                builder.PrependUOffsetTRelative(vocabFilelist[i])
            vocabFile = builder.EndVector()
        SentencePieceTokenizerOptionsStart(builder)
        if self.sentencePieceModel is not None:
            SentencePieceTokenizerOptionsAddSentencePieceModel(builder, sentencePieceModel)
        if self.vocabFile is not None:
            SentencePieceTokenizerOptionsAddVocabFile(builder, vocabFile)
        sentencePieceTokenizerOptions = SentencePieceTokenizerOptionsEnd(builder)
        return sentencePieceTokenizerOptions


class RegexTokenizerOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = RegexTokenizerOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsRegexTokenizerOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def RegexTokenizerOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # RegexTokenizerOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # RegexTokenizerOptions
    def DelimRegexPattern(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # RegexTokenizerOptions
    def VocabFile(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # RegexTokenizerOptions
    def VocabFileLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # RegexTokenizerOptions
    def VocabFileIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def RegexTokenizerOptionsStart(builder):
    builder.StartObject(2)

def RegexTokenizerOptionsAddDelimRegexPattern(builder, delimRegexPattern):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(delimRegexPattern), 0)

def RegexTokenizerOptionsAddVocabFile(builder, vocabFile):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(vocabFile), 0)

def RegexTokenizerOptionsStartVocabFileVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def RegexTokenizerOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class RegexTokenizerOptionsT(object):

    # RegexTokenizerOptionsT
    def __init__(self):
        self.delimRegexPattern = None  # type: str
        self.vocabFile = None  # type: List[AssociatedFileT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        regexTokenizerOptions = RegexTokenizerOptions()
        regexTokenizerOptions.Init(buf, pos)
        return cls.InitFromObj(regexTokenizerOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, regexTokenizerOptions):
        x = RegexTokenizerOptionsT()
        x._UnPack(regexTokenizerOptions)
        return x

    # RegexTokenizerOptionsT
    def _UnPack(self, regexTokenizerOptions):
        if regexTokenizerOptions is None:
            return
        self.delimRegexPattern = regexTokenizerOptions.DelimRegexPattern()
        if not regexTokenizerOptions.VocabFileIsNone():
            self.vocabFile = []
            for i in range(regexTokenizerOptions.VocabFileLength()):
                if regexTokenizerOptions.VocabFile(i) is None:
                    self.vocabFile.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(regexTokenizerOptions.VocabFile(i))
                    self.vocabFile.append(associatedFile_)

    # RegexTokenizerOptionsT
    def Pack(self, builder):
        if self.delimRegexPattern is not None:
            delimRegexPattern = builder.CreateString(self.delimRegexPattern)
        if self.vocabFile is not None:
            vocabFilelist = []
            for i in range(len(self.vocabFile)):
                vocabFilelist.append(self.vocabFile[i].Pack(builder))
            RegexTokenizerOptionsStartVocabFileVector(builder, len(self.vocabFile))
            for i in reversed(range(len(self.vocabFile))):
                builder.PrependUOffsetTRelative(vocabFilelist[i])
            vocabFile = builder.EndVector()
        RegexTokenizerOptionsStart(builder)
        if self.delimRegexPattern is not None:
            RegexTokenizerOptionsAddDelimRegexPattern(builder, delimRegexPattern)
        if self.vocabFile is not None:
            RegexTokenizerOptionsAddVocabFile(builder, vocabFile)
        regexTokenizerOptions = RegexTokenizerOptionsEnd(builder)
        return regexTokenizerOptions


class ProcessUnit(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ProcessUnit()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsProcessUnit(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ProcessUnitBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ProcessUnit
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ProcessUnit
    def OptionsType(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # ProcessUnit
    def Options(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            from flatbuffers.table import Table
            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None

def ProcessUnitStart(builder):
    builder.StartObject(2)

def ProcessUnitAddOptionsType(builder, optionsType):
    builder.PrependUint8Slot(0, optionsType, 0)

def ProcessUnitAddOptions(builder, options):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(options), 0)

def ProcessUnitEnd(builder):
    return builder.EndObject()


try:
    from typing import Union
except:
    pass

class ProcessUnitT(object):

    # ProcessUnitT
    def __init__(self):
        self.optionsType = 0  # type: int
        self.options = None  # type: Union[None, NormalizationOptionsT, ScoreCalibrationOptionsT, ScoreThresholdingOptionsT, BertTokenizerOptionsT, SentencePieceTokenizerOptionsT, RegexTokenizerOptionsT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        processUnit = ProcessUnit()
        processUnit.Init(buf, pos)
        return cls.InitFromObj(processUnit)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, processUnit):
        x = ProcessUnitT()
        x._UnPack(processUnit)
        return x

    # ProcessUnitT
    def _UnPack(self, processUnit):
        if processUnit is None:
            return
        self.optionsType = processUnit.OptionsType()
        self.options = ProcessUnitOptionsCreator(self.optionsType, processUnit.Options())

    # ProcessUnitT
    def Pack(self, builder):
        if self.options is not None:
            options = self.options.Pack(builder)
        ProcessUnitStart(builder)
        ProcessUnitAddOptionsType(builder, self.optionsType)
        if self.options is not None:
            ProcessUnitAddOptions(builder, options)
        processUnit = ProcessUnitEnd(builder)
        return processUnit


class Stats(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Stats()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsStats(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def StatsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # Stats
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Stats
    def Max(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Float32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # Stats
    def MaxAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Float32Flags, o)
        return 0

    # Stats
    def MaxLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # Stats
    def MaxIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # Stats
    def Min(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Float32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # Stats
    def MinAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Float32Flags, o)
        return 0

    # Stats
    def MinLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # Stats
    def MinIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def StatsStart(builder):
    builder.StartObject(2)

def StatsAddMax(builder, max):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(max), 0)

def StatsStartMaxVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StatsAddMin(builder, min):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(min), 0)

def StatsStartMinVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StatsEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class StatsT(object):

    # StatsT
    def __init__(self):
        self.max = None  # type: List[float]
        self.min = None  # type: List[float]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        stats = Stats()
        stats.Init(buf, pos)
        return cls.InitFromObj(stats)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, stats):
        x = StatsT()
        x._UnPack(stats)
        return x

    # StatsT
    def _UnPack(self, stats):
        if stats is None:
            return
        if not stats.MaxIsNone():
            if np is None:
                self.max = []
                for i in range(stats.MaxLength()):
                    self.max.append(stats.Max(i))
            else:
                self.max = stats.MaxAsNumpy()
        if not stats.MinIsNone():
            if np is None:
                self.min = []
                for i in range(stats.MinLength()):
                    self.min.append(stats.Min(i))
            else:
                self.min = stats.MinAsNumpy()

    # StatsT
    def Pack(self, builder):
        if self.max is not None:
            if np is not None and type(self.max) is np.ndarray:
                max = builder.CreateNumpyVector(self.max)
            else:
                StatsStartMaxVector(builder, len(self.max))
                for i in reversed(range(len(self.max))):
                    builder.PrependFloat32(self.max[i])
                max = builder.EndVector()
        if self.min is not None:
            if np is not None and type(self.min) is np.ndarray:
                min = builder.CreateNumpyVector(self.min)
            else:
                StatsStartMinVector(builder, len(self.min))
                for i in reversed(range(len(self.min))):
                    builder.PrependFloat32(self.min[i])
                min = builder.EndVector()
        StatsStart(builder)
        if self.max is not None:
            StatsAddMax(builder, max)
        if self.min is not None:
            StatsAddMin(builder, min)
        stats = StatsEnd(builder)
        return stats


class TensorGroup(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = TensorGroup()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsTensorGroup(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def TensorGroupBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # TensorGroup
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # TensorGroup
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # TensorGroup
    def TensorNames(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.String(a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return ""

    # TensorGroup
    def TensorNamesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # TensorGroup
    def TensorNamesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def TensorGroupStart(builder):
    builder.StartObject(2)

def TensorGroupAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def TensorGroupAddTensorNames(builder, tensorNames):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(tensorNames), 0)

def TensorGroupStartTensorNamesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def TensorGroupEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class TensorGroupT(object):

    # TensorGroupT
    def __init__(self):
        self.name = None  # type: str
        self.tensorNames = None  # type: List[str]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        tensorGroup = TensorGroup()
        tensorGroup.Init(buf, pos)
        return cls.InitFromObj(tensorGroup)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, tensorGroup):
        x = TensorGroupT()
        x._UnPack(tensorGroup)
        return x

    # TensorGroupT
    def _UnPack(self, tensorGroup):
        if tensorGroup is None:
            return
        self.name = tensorGroup.Name()
        if not tensorGroup.TensorNamesIsNone():
            self.tensorNames = []
            for i in range(tensorGroup.TensorNamesLength()):
                self.tensorNames.append(tensorGroup.TensorNames(i))

    # TensorGroupT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.tensorNames is not None:
            tensorNameslist = []
            for i in range(len(self.tensorNames)):
                tensorNameslist.append(builder.CreateString(self.tensorNames[i]))
            TensorGroupStartTensorNamesVector(builder, len(self.tensorNames))
            for i in reversed(range(len(self.tensorNames))):
                builder.PrependUOffsetTRelative(tensorNameslist[i])
            tensorNames = builder.EndVector()
        TensorGroupStart(builder)
        if self.name is not None:
            TensorGroupAddName(builder, name)
        if self.tensorNames is not None:
            TensorGroupAddTensorNames(builder, tensorNames)
        tensorGroup = TensorGroupEnd(builder)
        return tensorGroup


class TensorMetadata(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = TensorMetadata()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsTensorMetadata(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def TensorMetadataBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # TensorMetadata
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # TensorMetadata
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # TensorMetadata
    def Description(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # TensorMetadata
    def DimensionNames(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.String(a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return ""

    # TensorMetadata
    def DimensionNamesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # TensorMetadata
    def DimensionNamesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        return o == 0

    # TensorMetadata
    def Content(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = Content()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # TensorMetadata
    def ProcessUnits(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = ProcessUnit()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # TensorMetadata
    def ProcessUnitsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # TensorMetadata
    def ProcessUnitsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        return o == 0

    # TensorMetadata
    def Stats(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = Stats()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # TensorMetadata
    def AssociatedFiles(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # TensorMetadata
    def AssociatedFilesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # TensorMetadata
    def AssociatedFilesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        return o == 0

def TensorMetadataStart(builder):
    builder.StartObject(7)

def TensorMetadataAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def TensorMetadataAddDescription(builder, description):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(description), 0)

def TensorMetadataAddDimensionNames(builder, dimensionNames):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(dimensionNames), 0)

def TensorMetadataStartDimensionNamesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def TensorMetadataAddContent(builder, content):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(content), 0)

def TensorMetadataAddProcessUnits(builder, processUnits):
    builder.PrependUOffsetTRelativeSlot(4, flatbuffers.number_types.UOffsetTFlags.py_type(processUnits), 0)

def TensorMetadataStartProcessUnitsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def TensorMetadataAddStats(builder, stats):
    builder.PrependUOffsetTRelativeSlot(5, flatbuffers.number_types.UOffsetTFlags.py_type(stats), 0)

def TensorMetadataAddAssociatedFiles(builder, associatedFiles):
    builder.PrependUOffsetTRelativeSlot(6, flatbuffers.number_types.UOffsetTFlags.py_type(associatedFiles), 0)

def TensorMetadataStartAssociatedFilesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def TensorMetadataEnd(builder):
    return builder.EndObject()


try:
    from typing import List, Optional
except:
    pass

class TensorMetadataT(object):

    # TensorMetadataT
    def __init__(self):
        self.name = None  # type: str
        self.description = None  # type: str
        self.dimensionNames = None  # type: List[str]
        self.content = None  # type: Optional[ContentT]
        self.processUnits = None  # type: List[ProcessUnitT]
        self.stats = None  # type: Optional[StatsT]
        self.associatedFiles = None  # type: List[AssociatedFileT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        tensorMetadata = TensorMetadata()
        tensorMetadata.Init(buf, pos)
        return cls.InitFromObj(tensorMetadata)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, tensorMetadata):
        x = TensorMetadataT()
        x._UnPack(tensorMetadata)
        return x

    # TensorMetadataT
    def _UnPack(self, tensorMetadata):
        if tensorMetadata is None:
            return
        self.name = tensorMetadata.Name()
        self.description = tensorMetadata.Description()
        if not tensorMetadata.DimensionNamesIsNone():
            self.dimensionNames = []
            for i in range(tensorMetadata.DimensionNamesLength()):
                self.dimensionNames.append(tensorMetadata.DimensionNames(i))
        if tensorMetadata.Content() is not None:
            self.content = ContentT.InitFromObj(tensorMetadata.Content())
        if not tensorMetadata.ProcessUnitsIsNone():
            self.processUnits = []
            for i in range(tensorMetadata.ProcessUnitsLength()):
                if tensorMetadata.ProcessUnits(i) is None:
                    self.processUnits.append(None)
                else:
                    processUnit_ = ProcessUnitT.InitFromObj(tensorMetadata.ProcessUnits(i))
                    self.processUnits.append(processUnit_)
        if tensorMetadata.Stats() is not None:
            self.stats = StatsT.InitFromObj(tensorMetadata.Stats())
        if not tensorMetadata.AssociatedFilesIsNone():
            self.associatedFiles = []
            for i in range(tensorMetadata.AssociatedFilesLength()):
                if tensorMetadata.AssociatedFiles(i) is None:
                    self.associatedFiles.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(tensorMetadata.AssociatedFiles(i))
                    self.associatedFiles.append(associatedFile_)

    # TensorMetadataT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.description is not None:
            description = builder.CreateString(self.description)
        if self.dimensionNames is not None:
            dimensionNameslist = []
            for i in range(len(self.dimensionNames)):
                dimensionNameslist.append(builder.CreateString(self.dimensionNames[i]))
            TensorMetadataStartDimensionNamesVector(builder, len(self.dimensionNames))
            for i in reversed(range(len(self.dimensionNames))):
                builder.PrependUOffsetTRelative(dimensionNameslist[i])
            dimensionNames = builder.EndVector()
        if self.content is not None:
            content = self.content.Pack(builder)
        if self.processUnits is not None:
            processUnitslist = []
            for i in range(len(self.processUnits)):
                processUnitslist.append(self.processUnits[i].Pack(builder))
            TensorMetadataStartProcessUnitsVector(builder, len(self.processUnits))
            for i in reversed(range(len(self.processUnits))):
                builder.PrependUOffsetTRelative(processUnitslist[i])
            processUnits = builder.EndVector()
        if self.stats is not None:
            stats = self.stats.Pack(builder)
        if self.associatedFiles is not None:
            associatedFileslist = []
            for i in range(len(self.associatedFiles)):
                associatedFileslist.append(self.associatedFiles[i].Pack(builder))
            TensorMetadataStartAssociatedFilesVector(builder, len(self.associatedFiles))
            for i in reversed(range(len(self.associatedFiles))):
                builder.PrependUOffsetTRelative(associatedFileslist[i])
            associatedFiles = builder.EndVector()
        TensorMetadataStart(builder)
        if self.name is not None:
            TensorMetadataAddName(builder, name)
        if self.description is not None:
            TensorMetadataAddDescription(builder, description)
        if self.dimensionNames is not None:
            TensorMetadataAddDimensionNames(builder, dimensionNames)
        if self.content is not None:
            TensorMetadataAddContent(builder, content)
        if self.processUnits is not None:
            TensorMetadataAddProcessUnits(builder, processUnits)
        if self.stats is not None:
            TensorMetadataAddStats(builder, stats)
        if self.associatedFiles is not None:
            TensorMetadataAddAssociatedFiles(builder, associatedFiles)
        tensorMetadata = TensorMetadataEnd(builder)
        return tensorMetadata


class CustomMetadata(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = CustomMetadata()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsCustomMetadata(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def CustomMetadataBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # CustomMetadata
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # CustomMetadata
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # CustomMetadata
    def Data(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 1))
        return 0

    # CustomMetadata
    def DataAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Uint8Flags, o)
        return 0

    # CustomMetadata
    def DataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # CustomMetadata
    def DataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def CustomMetadataStart(builder):
    builder.StartObject(2)

def CustomMetadataAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def CustomMetadataAddData(builder, data):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(data), 0)

def CustomMetadataStartDataVector(builder, numElems):
    return builder.StartVector(1, numElems, 1)

def CustomMetadataEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class CustomMetadataT(object):

    # CustomMetadataT
    def __init__(self):
        self.name = None  # type: str
        self.data = None  # type: List[int]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        customMetadata = CustomMetadata()
        customMetadata.Init(buf, pos)
        return cls.InitFromObj(customMetadata)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, customMetadata):
        x = CustomMetadataT()
        x._UnPack(customMetadata)
        return x

    # CustomMetadataT
    def _UnPack(self, customMetadata):
        if customMetadata is None:
            return
        self.name = customMetadata.Name()
        if not customMetadata.DataIsNone():
            if np is None:
                self.data = []
                for i in range(customMetadata.DataLength()):
                    self.data.append(customMetadata.Data(i))
            else:
                self.data = customMetadata.DataAsNumpy()

    # CustomMetadataT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.data is not None:
            if np is not None and type(self.data) is np.ndarray:
                data = builder.CreateNumpyVector(self.data)
            else:
                CustomMetadataStartDataVector(builder, len(self.data))
                for i in reversed(range(len(self.data))):
                    builder.PrependUint8(self.data[i])
                data = builder.EndVector()
        CustomMetadataStart(builder)
        if self.name is not None:
            CustomMetadataAddName(builder, name)
        if self.data is not None:
            CustomMetadataAddData(builder, data)
        customMetadata = CustomMetadataEnd(builder)
        return customMetadata


class SubGraphMetadata(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = SubGraphMetadata()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSubGraphMetadata(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def SubGraphMetadataBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # SubGraphMetadata
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # SubGraphMetadata
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # SubGraphMetadata
    def Description(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # SubGraphMetadata
    def InputTensorMetadata(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = TensorMetadata()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def InputTensorMetadataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def InputTensorMetadataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        return o == 0

    # SubGraphMetadata
    def OutputTensorMetadata(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = TensorMetadata()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def OutputTensorMetadataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def OutputTensorMetadataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        return o == 0

    # SubGraphMetadata
    def AssociatedFiles(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def AssociatedFilesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def AssociatedFilesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        return o == 0

    # SubGraphMetadata
    def InputProcessUnits(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = ProcessUnit()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def InputProcessUnitsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def InputProcessUnitsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        return o == 0

    # SubGraphMetadata
    def OutputProcessUnits(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = ProcessUnit()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def OutputProcessUnitsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def OutputProcessUnitsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        return o == 0

    # SubGraphMetadata
    def InputTensorGroups(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = TensorGroup()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def InputTensorGroupsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def InputTensorGroupsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        return o == 0

    # SubGraphMetadata
    def OutputTensorGroups(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = TensorGroup()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def OutputTensorGroupsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def OutputTensorGroupsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        return o == 0

    # SubGraphMetadata
    def CustomMetadata(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = CustomMetadata()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # SubGraphMetadata
    def CustomMetadataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # SubGraphMetadata
    def CustomMetadataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        return o == 0

def SubGraphMetadataStart(builder):
    builder.StartObject(10)

def SubGraphMetadataAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def SubGraphMetadataAddDescription(builder, description):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(description), 0)

def SubGraphMetadataAddInputTensorMetadata(builder, inputTensorMetadata):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(inputTensorMetadata), 0)

def SubGraphMetadataStartInputTensorMetadataVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddOutputTensorMetadata(builder, outputTensorMetadata):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(outputTensorMetadata), 0)

def SubGraphMetadataStartOutputTensorMetadataVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddAssociatedFiles(builder, associatedFiles):
    builder.PrependUOffsetTRelativeSlot(4, flatbuffers.number_types.UOffsetTFlags.py_type(associatedFiles), 0)

def SubGraphMetadataStartAssociatedFilesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddInputProcessUnits(builder, inputProcessUnits):
    builder.PrependUOffsetTRelativeSlot(5, flatbuffers.number_types.UOffsetTFlags.py_type(inputProcessUnits), 0)

def SubGraphMetadataStartInputProcessUnitsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddOutputProcessUnits(builder, outputProcessUnits):
    builder.PrependUOffsetTRelativeSlot(6, flatbuffers.number_types.UOffsetTFlags.py_type(outputProcessUnits), 0)

def SubGraphMetadataStartOutputProcessUnitsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddInputTensorGroups(builder, inputTensorGroups):
    builder.PrependUOffsetTRelativeSlot(7, flatbuffers.number_types.UOffsetTFlags.py_type(inputTensorGroups), 0)

def SubGraphMetadataStartInputTensorGroupsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddOutputTensorGroups(builder, outputTensorGroups):
    builder.PrependUOffsetTRelativeSlot(8, flatbuffers.number_types.UOffsetTFlags.py_type(outputTensorGroups), 0)

def SubGraphMetadataStartOutputTensorGroupsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataAddCustomMetadata(builder, customMetadata):
    builder.PrependUOffsetTRelativeSlot(9, flatbuffers.number_types.UOffsetTFlags.py_type(customMetadata), 0)

def SubGraphMetadataStartCustomMetadataVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def SubGraphMetadataEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class SubGraphMetadataT(object):

    # SubGraphMetadataT
    def __init__(self):
        self.name = None  # type: str
        self.description = None  # type: str
        self.inputTensorMetadata = None  # type: List[TensorMetadataT]
        self.outputTensorMetadata = None  # type: List[TensorMetadataT]
        self.associatedFiles = None  # type: List[AssociatedFileT]
        self.inputProcessUnits = None  # type: List[ProcessUnitT]
        self.outputProcessUnits = None  # type: List[ProcessUnitT]
        self.inputTensorGroups = None  # type: List[TensorGroupT]
        self.outputTensorGroups = None  # type: List[TensorGroupT]
        self.customMetadata = None  # type: List[CustomMetadataT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        subGraphMetadata = SubGraphMetadata()
        subGraphMetadata.Init(buf, pos)
        return cls.InitFromObj(subGraphMetadata)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, subGraphMetadata):
        x = SubGraphMetadataT()
        x._UnPack(subGraphMetadata)
        return x

    # SubGraphMetadataT
    def _UnPack(self, subGraphMetadata):
        if subGraphMetadata is None:
            return
        self.name = subGraphMetadata.Name()
        self.description = subGraphMetadata.Description()
        if not subGraphMetadata.InputTensorMetadataIsNone():
            self.inputTensorMetadata = []
            for i in range(subGraphMetadata.InputTensorMetadataLength()):
                if subGraphMetadata.InputTensorMetadata(i) is None:
                    self.inputTensorMetadata.append(None)
                else:
                    tensorMetadata_ = TensorMetadataT.InitFromObj(subGraphMetadata.InputTensorMetadata(i))
                    self.inputTensorMetadata.append(tensorMetadata_)
        if not subGraphMetadata.OutputTensorMetadataIsNone():
            self.outputTensorMetadata = []
            for i in range(subGraphMetadata.OutputTensorMetadataLength()):
                if subGraphMetadata.OutputTensorMetadata(i) is None:
                    self.outputTensorMetadata.append(None)
                else:
                    tensorMetadata_ = TensorMetadataT.InitFromObj(subGraphMetadata.OutputTensorMetadata(i))
                    self.outputTensorMetadata.append(tensorMetadata_)
        if not subGraphMetadata.AssociatedFilesIsNone():
            self.associatedFiles = []
            for i in range(subGraphMetadata.AssociatedFilesLength()):
                if subGraphMetadata.AssociatedFiles(i) is None:
                    self.associatedFiles.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(subGraphMetadata.AssociatedFiles(i))
                    self.associatedFiles.append(associatedFile_)
        if not subGraphMetadata.InputProcessUnitsIsNone():
            self.inputProcessUnits = []
            for i in range(subGraphMetadata.InputProcessUnitsLength()):
                if subGraphMetadata.InputProcessUnits(i) is None:
                    self.inputProcessUnits.append(None)
                else:
                    processUnit_ = ProcessUnitT.InitFromObj(subGraphMetadata.InputProcessUnits(i))
                    self.inputProcessUnits.append(processUnit_)
        if not subGraphMetadata.OutputProcessUnitsIsNone():
            self.outputProcessUnits = []
            for i in range(subGraphMetadata.OutputProcessUnitsLength()):
                if subGraphMetadata.OutputProcessUnits(i) is None:
                    self.outputProcessUnits.append(None)
                else:
                    processUnit_ = ProcessUnitT.InitFromObj(subGraphMetadata.OutputProcessUnits(i))
                    self.outputProcessUnits.append(processUnit_)
        if not subGraphMetadata.InputTensorGroupsIsNone():
            self.inputTensorGroups = []
            for i in range(subGraphMetadata.InputTensorGroupsLength()):
                if subGraphMetadata.InputTensorGroups(i) is None:
                    self.inputTensorGroups.append(None)
                else:
                    tensorGroup_ = TensorGroupT.InitFromObj(subGraphMetadata.InputTensorGroups(i))
                    self.inputTensorGroups.append(tensorGroup_)
        if not subGraphMetadata.OutputTensorGroupsIsNone():
            self.outputTensorGroups = []
            for i in range(subGraphMetadata.OutputTensorGroupsLength()):
                if subGraphMetadata.OutputTensorGroups(i) is None:
                    self.outputTensorGroups.append(None)
                else:
                    tensorGroup_ = TensorGroupT.InitFromObj(subGraphMetadata.OutputTensorGroups(i))
                    self.outputTensorGroups.append(tensorGroup_)
        if not subGraphMetadata.CustomMetadataIsNone():
            self.customMetadata = []
            for i in range(subGraphMetadata.CustomMetadataLength()):
                if subGraphMetadata.CustomMetadata(i) is None:
                    self.customMetadata.append(None)
                else:
                    customMetadata_ = CustomMetadataT.InitFromObj(subGraphMetadata.CustomMetadata(i))
                    self.customMetadata.append(customMetadata_)

    # SubGraphMetadataT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.description is not None:
            description = builder.CreateString(self.description)
        if self.inputTensorMetadata is not None:
            inputTensorMetadatalist = []
            for i in range(len(self.inputTensorMetadata)):
                inputTensorMetadatalist.append(self.inputTensorMetadata[i].Pack(builder))
            SubGraphMetadataStartInputTensorMetadataVector(builder, len(self.inputTensorMetadata))
            for i in reversed(range(len(self.inputTensorMetadata))):
                builder.PrependUOffsetTRelative(inputTensorMetadatalist[i])
            inputTensorMetadata = builder.EndVector()
        if self.outputTensorMetadata is not None:
            outputTensorMetadatalist = []
            for i in range(len(self.outputTensorMetadata)):
                outputTensorMetadatalist.append(self.outputTensorMetadata[i].Pack(builder))
            SubGraphMetadataStartOutputTensorMetadataVector(builder, len(self.outputTensorMetadata))
            for i in reversed(range(len(self.outputTensorMetadata))):
                builder.PrependUOffsetTRelative(outputTensorMetadatalist[i])
            outputTensorMetadata = builder.EndVector()
        if self.associatedFiles is not None:
            associatedFileslist = []
            for i in range(len(self.associatedFiles)):
                associatedFileslist.append(self.associatedFiles[i].Pack(builder))
            SubGraphMetadataStartAssociatedFilesVector(builder, len(self.associatedFiles))
            for i in reversed(range(len(self.associatedFiles))):
                builder.PrependUOffsetTRelative(associatedFileslist[i])
            associatedFiles = builder.EndVector()
        if self.inputProcessUnits is not None:
            inputProcessUnitslist = []
            for i in range(len(self.inputProcessUnits)):
                inputProcessUnitslist.append(self.inputProcessUnits[i].Pack(builder))
            SubGraphMetadataStartInputProcessUnitsVector(builder, len(self.inputProcessUnits))
            for i in reversed(range(len(self.inputProcessUnits))):
                builder.PrependUOffsetTRelative(inputProcessUnitslist[i])
            inputProcessUnits = builder.EndVector()
        if self.outputProcessUnits is not None:
            outputProcessUnitslist = []
            for i in range(len(self.outputProcessUnits)):
                outputProcessUnitslist.append(self.outputProcessUnits[i].Pack(builder))
            SubGraphMetadataStartOutputProcessUnitsVector(builder, len(self.outputProcessUnits))
            for i in reversed(range(len(self.outputProcessUnits))):
                builder.PrependUOffsetTRelative(outputProcessUnitslist[i])
            outputProcessUnits = builder.EndVector()
        if self.inputTensorGroups is not None:
            inputTensorGroupslist = []
            for i in range(len(self.inputTensorGroups)):
                inputTensorGroupslist.append(self.inputTensorGroups[i].Pack(builder))
            SubGraphMetadataStartInputTensorGroupsVector(builder, len(self.inputTensorGroups))
            for i in reversed(range(len(self.inputTensorGroups))):
                builder.PrependUOffsetTRelative(inputTensorGroupslist[i])
            inputTensorGroups = builder.EndVector()
        if self.outputTensorGroups is not None:
            outputTensorGroupslist = []
            for i in range(len(self.outputTensorGroups)):
                outputTensorGroupslist.append(self.outputTensorGroups[i].Pack(builder))
            SubGraphMetadataStartOutputTensorGroupsVector(builder, len(self.outputTensorGroups))
            for i in reversed(range(len(self.outputTensorGroups))):
                builder.PrependUOffsetTRelative(outputTensorGroupslist[i])
            outputTensorGroups = builder.EndVector()
        if self.customMetadata is not None:
            customMetadatalist = []
            for i in range(len(self.customMetadata)):
                customMetadatalist.append(self.customMetadata[i].Pack(builder))
            SubGraphMetadataStartCustomMetadataVector(builder, len(self.customMetadata))
            for i in reversed(range(len(self.customMetadata))):
                builder.PrependUOffsetTRelative(customMetadatalist[i])
            customMetadata = builder.EndVector()
        SubGraphMetadataStart(builder)
        if self.name is not None:
            SubGraphMetadataAddName(builder, name)
        if self.description is not None:
            SubGraphMetadataAddDescription(builder, description)
        if self.inputTensorMetadata is not None:
            SubGraphMetadataAddInputTensorMetadata(builder, inputTensorMetadata)
        if self.outputTensorMetadata is not None:
            SubGraphMetadataAddOutputTensorMetadata(builder, outputTensorMetadata)
        if self.associatedFiles is not None:
            SubGraphMetadataAddAssociatedFiles(builder, associatedFiles)
        if self.inputProcessUnits is not None:
            SubGraphMetadataAddInputProcessUnits(builder, inputProcessUnits)
        if self.outputProcessUnits is not None:
            SubGraphMetadataAddOutputProcessUnits(builder, outputProcessUnits)
        if self.inputTensorGroups is not None:
            SubGraphMetadataAddInputTensorGroups(builder, inputTensorGroups)
        if self.outputTensorGroups is not None:
            SubGraphMetadataAddOutputTensorGroups(builder, outputTensorGroups)
        if self.customMetadata is not None:
            SubGraphMetadataAddCustomMetadata(builder, customMetadata)
        subGraphMetadata = SubGraphMetadataEnd(builder)
        return subGraphMetadata


class ModelMetadata(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ModelMetadata()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsModelMetadata(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ModelMetadataBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4D\x30\x30\x31", size_prefixed=size_prefixed)

    # ModelMetadata
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ModelMetadata
    def Name(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ModelMetadata
    def Description(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ModelMetadata
    def Version(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ModelMetadata
    def SubgraphMetadata(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = SubGraphMetadata()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # ModelMetadata
    def SubgraphMetadataLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # ModelMetadata
    def SubgraphMetadataIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        return o == 0

    # ModelMetadata
    def Author(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ModelMetadata
    def License(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ModelMetadata
    def AssociatedFiles(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = AssociatedFile()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # ModelMetadata
    def AssociatedFilesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # ModelMetadata
    def AssociatedFilesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        return o == 0

    # ModelMetadata
    def MinParserVersion(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def ModelMetadataStart(builder):
    builder.StartObject(8)

def ModelMetadataAddName(builder, name):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(name), 0)

def ModelMetadataAddDescription(builder, description):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(description), 0)

def ModelMetadataAddVersion(builder, version):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(version), 0)

def ModelMetadataAddSubgraphMetadata(builder, subgraphMetadata):
    builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(subgraphMetadata), 0)

def ModelMetadataStartSubgraphMetadataVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def ModelMetadataAddAuthor(builder, author):
    builder.PrependUOffsetTRelativeSlot(4, flatbuffers.number_types.UOffsetTFlags.py_type(author), 0)

def ModelMetadataAddLicense(builder, license):
    builder.PrependUOffsetTRelativeSlot(5, flatbuffers.number_types.UOffsetTFlags.py_type(license), 0)

def ModelMetadataAddAssociatedFiles(builder, associatedFiles):
    builder.PrependUOffsetTRelativeSlot(6, flatbuffers.number_types.UOffsetTFlags.py_type(associatedFiles), 0)

def ModelMetadataStartAssociatedFilesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def ModelMetadataAddMinParserVersion(builder, minParserVersion):
    builder.PrependUOffsetTRelativeSlot(7, flatbuffers.number_types.UOffsetTFlags.py_type(minParserVersion), 0)

def ModelMetadataEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class ModelMetadataT(object):

    # ModelMetadataT
    def __init__(self):
        self.name = None  # type: str
        self.description = None  # type: str
        self.version = None  # type: str
        self.subgraphMetadata = None  # type: List[SubGraphMetadataT]
        self.author = None  # type: str
        self.license = None  # type: str
        self.associatedFiles = None  # type: List[AssociatedFileT]
        self.minParserVersion = None  # type: str

    @classmethod
    def InitFromBuf(cls, buf, pos):
        modelMetadata = ModelMetadata()
        modelMetadata.Init(buf, pos)
        return cls.InitFromObj(modelMetadata)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, modelMetadata):
        x = ModelMetadataT()
        x._UnPack(modelMetadata)
        return x

    # ModelMetadataT
    def _UnPack(self, modelMetadata):
        if modelMetadata is None:
            return
        self.name = modelMetadata.Name()
        self.description = modelMetadata.Description()
        self.version = modelMetadata.Version()
        if not modelMetadata.SubgraphMetadataIsNone():
            self.subgraphMetadata = []
            for i in range(modelMetadata.SubgraphMetadataLength()):
                if modelMetadata.SubgraphMetadata(i) is None:
                    self.subgraphMetadata.append(None)
                else:
                    subGraphMetadata_ = SubGraphMetadataT.InitFromObj(modelMetadata.SubgraphMetadata(i))
                    self.subgraphMetadata.append(subGraphMetadata_)
        self.author = modelMetadata.Author()
        self.license = modelMetadata.License()
        if not modelMetadata.AssociatedFilesIsNone():
            self.associatedFiles = []
            for i in range(modelMetadata.AssociatedFilesLength()):
                if modelMetadata.AssociatedFiles(i) is None:
                    self.associatedFiles.append(None)
                else:
                    associatedFile_ = AssociatedFileT.InitFromObj(modelMetadata.AssociatedFiles(i))
                    self.associatedFiles.append(associatedFile_)
        self.minParserVersion = modelMetadata.MinParserVersion()

    # ModelMetadataT
    def Pack(self, builder):
        if self.name is not None:
            name = builder.CreateString(self.name)
        if self.description is not None:
            description = builder.CreateString(self.description)
        if self.version is not None:
            version = builder.CreateString(self.version)
        if self.subgraphMetadata is not None:
            subgraphMetadatalist = []
            for i in range(len(self.subgraphMetadata)):
                subgraphMetadatalist.append(self.subgraphMetadata[i].Pack(builder))
            ModelMetadataStartSubgraphMetadataVector(builder, len(self.subgraphMetadata))
            for i in reversed(range(len(self.subgraphMetadata))):
                builder.PrependUOffsetTRelative(subgraphMetadatalist[i])
            subgraphMetadata = builder.EndVector()
        if self.author is not None:
            author = builder.CreateString(self.author)
        if self.license is not None:
            license = builder.CreateString(self.license)
        if self.associatedFiles is not None:
            associatedFileslist = []
            for i in range(len(self.associatedFiles)):
                associatedFileslist.append(self.associatedFiles[i].Pack(builder))
            ModelMetadataStartAssociatedFilesVector(builder, len(self.associatedFiles))
            for i in reversed(range(len(self.associatedFiles))):
                builder.PrependUOffsetTRelative(associatedFileslist[i])
            associatedFiles = builder.EndVector()
        if self.minParserVersion is not None:
            minParserVersion = builder.CreateString(self.minParserVersion)
        ModelMetadataStart(builder)
        if self.name is not None:
            ModelMetadataAddName(builder, name)
        if self.description is not None:
            ModelMetadataAddDescription(builder, description)
        if self.version is not None:
            ModelMetadataAddVersion(builder, version)
        if self.subgraphMetadata is not None:
            ModelMetadataAddSubgraphMetadata(builder, subgraphMetadata)
        if self.author is not None:
            ModelMetadataAddAuthor(builder, author)
        if self.license is not None:
            ModelMetadataAddLicense(builder, license)
        if self.associatedFiles is not None:
            ModelMetadataAddAssociatedFiles(builder, associatedFiles)
        if self.minParserVersion is not None:
            ModelMetadataAddMinParserVersion(builder, minParserVersion)
        modelMetadata = ModelMetadataEnd(builder)
        return modelMetadata


