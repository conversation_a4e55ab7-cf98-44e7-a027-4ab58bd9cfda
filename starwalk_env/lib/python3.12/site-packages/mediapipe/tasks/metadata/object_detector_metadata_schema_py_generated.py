import flatbuffers

# automatically generated by the FlatBuffers compiler, do not modify

# namespace: tasks

from flatbuffers.compat import import_numpy
np = import_numpy()

class FixedAnchor(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = FixedAnchor()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsFixedAnchor(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def FixedAnchorBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # FixedAnchor
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # FixedAnchor
    def XCenter(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # FixedAnchor
    def YCenter(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # FixedAnchor
    def Width(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # FixedAnchor
    def Height(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

def FixedAnchorStart(builder):
    builder.StartObject(4)

def FixedAnchorAddXCenter(builder, xCenter):
    builder.PrependFloat32Slot(0, xCenter, 0.0)

def FixedAnchorAddYCenter(builder, yCenter):
    builder.PrependFloat32Slot(1, yCenter, 0.0)

def FixedAnchorAddWidth(builder, width):
    builder.PrependFloat32Slot(2, width, 0.0)

def FixedAnchorAddHeight(builder, height):
    builder.PrependFloat32Slot(3, height, 0.0)

def FixedAnchorEnd(builder):
    return builder.EndObject()



class FixedAnchorT(object):

    # FixedAnchorT
    def __init__(self):
        self.xCenter = 0.0  # type: float
        self.yCenter = 0.0  # type: float
        self.width = 0.0  # type: float
        self.height = 0.0  # type: float

    @classmethod
    def InitFromBuf(cls, buf, pos):
        fixedAnchor = FixedAnchor()
        fixedAnchor.Init(buf, pos)
        return cls.InitFromObj(fixedAnchor)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, fixedAnchor):
        x = FixedAnchorT()
        x._UnPack(fixedAnchor)
        return x

    # FixedAnchorT
    def _UnPack(self, fixedAnchor):
        if fixedAnchor is None:
            return
        self.xCenter = fixedAnchor.XCenter()
        self.yCenter = fixedAnchor.YCenter()
        self.width = fixedAnchor.Width()
        self.height = fixedAnchor.Height()

    # FixedAnchorT
    def Pack(self, builder):
        FixedAnchorStart(builder)
        FixedAnchorAddXCenter(builder, self.xCenter)
        FixedAnchorAddYCenter(builder, self.yCenter)
        FixedAnchorAddWidth(builder, self.width)
        FixedAnchorAddHeight(builder, self.height)
        fixedAnchor = FixedAnchorEnd(builder)
        return fixedAnchor


class FixedAnchorsSchema(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = FixedAnchorsSchema()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsFixedAnchorsSchema(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def FixedAnchorsSchemaBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # FixedAnchorsSchema
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # FixedAnchorsSchema
    def Anchors(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            obj = FixedAnchor()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # FixedAnchorsSchema
    def AnchorsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # FixedAnchorsSchema
    def AnchorsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

def FixedAnchorsSchemaStart(builder):
    builder.StartObject(1)

def FixedAnchorsSchemaAddAnchors(builder, anchors):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(anchors), 0)

def FixedAnchorsSchemaStartAnchorsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def FixedAnchorsSchemaEnd(builder):
    return builder.EndObject()


try:
    from typing import List
except:
    pass

class FixedAnchorsSchemaT(object):

    # FixedAnchorsSchemaT
    def __init__(self):
        self.anchors = None  # type: List[FixedAnchorT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        fixedAnchorsSchema = FixedAnchorsSchema()
        fixedAnchorsSchema.Init(buf, pos)
        return cls.InitFromObj(fixedAnchorsSchema)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, fixedAnchorsSchema):
        x = FixedAnchorsSchemaT()
        x._UnPack(fixedAnchorsSchema)
        return x

    # FixedAnchorsSchemaT
    def _UnPack(self, fixedAnchorsSchema):
        if fixedAnchorsSchema is None:
            return
        if not fixedAnchorsSchema.AnchorsIsNone():
            self.anchors = []
            for i in range(fixedAnchorsSchema.AnchorsLength()):
                if fixedAnchorsSchema.Anchors(i) is None:
                    self.anchors.append(None)
                else:
                    fixedAnchor_ = FixedAnchorT.InitFromObj(fixedAnchorsSchema.Anchors(i))
                    self.anchors.append(fixedAnchor_)

    # FixedAnchorsSchemaT
    def Pack(self, builder):
        if self.anchors is not None:
            anchorslist = []
            for i in range(len(self.anchors)):
                anchorslist.append(self.anchors[i].Pack(builder))
            FixedAnchorsSchemaStartAnchorsVector(builder, len(self.anchors))
            for i in reversed(range(len(self.anchors))):
                builder.PrependUOffsetTRelative(anchorslist[i])
            anchors = builder.EndVector()
        FixedAnchorsSchemaStart(builder)
        if self.anchors is not None:
            FixedAnchorsSchemaAddAnchors(builder, anchors)
        fixedAnchorsSchema = FixedAnchorsSchemaEnd(builder)
        return fixedAnchorsSchema


class SsdAnchorsOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = SsdAnchorsOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsSsdAnchorsOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def SsdAnchorsOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # SsdAnchorsOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # SsdAnchorsOptions
    def FixedAnchorsSchema(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = FixedAnchorsSchema()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def SsdAnchorsOptionsStart(builder):
    builder.StartObject(1)

def SsdAnchorsOptionsAddFixedAnchorsSchema(builder, fixedAnchorsSchema):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(fixedAnchorsSchema), 0)

def SsdAnchorsOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import Optional
except:
    pass

class SsdAnchorsOptionsT(object):

    # SsdAnchorsOptionsT
    def __init__(self):
        self.fixedAnchorsSchema = None  # type: Optional[FixedAnchorsSchemaT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        ssdAnchorsOptions = SsdAnchorsOptions()
        ssdAnchorsOptions.Init(buf, pos)
        return cls.InitFromObj(ssdAnchorsOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, ssdAnchorsOptions):
        x = SsdAnchorsOptionsT()
        x._UnPack(ssdAnchorsOptions)
        return x

    # SsdAnchorsOptionsT
    def _UnPack(self, ssdAnchorsOptions):
        if ssdAnchorsOptions is None:
            return
        if ssdAnchorsOptions.FixedAnchorsSchema() is not None:
            self.fixedAnchorsSchema = FixedAnchorsSchemaT.InitFromObj(ssdAnchorsOptions.FixedAnchorsSchema())

    # SsdAnchorsOptionsT
    def Pack(self, builder):
        if self.fixedAnchorsSchema is not None:
            fixedAnchorsSchema = self.fixedAnchorsSchema.Pack(builder)
        SsdAnchorsOptionsStart(builder)
        if self.fixedAnchorsSchema is not None:
            SsdAnchorsOptionsAddFixedAnchorsSchema(builder, fixedAnchorsSchema)
        ssdAnchorsOptions = SsdAnchorsOptionsEnd(builder)
        return ssdAnchorsOptions


class TensorsDecodingOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = TensorsDecodingOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsTensorsDecodingOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def TensorsDecodingOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # TensorsDecodingOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # TensorsDecodingOptions
    def NumClasses(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def NumBoxes(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def NumCoords(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def KeypointCoordOffset(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def NumKeypoints(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def NumValuesPerKeypoint(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int32Flags, o + self._tab.Pos)
        return 0

    # TensorsDecodingOptions
    def XScale(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # TensorsDecodingOptions
    def YScale(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # TensorsDecodingOptions
    def WScale(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # TensorsDecodingOptions
    def HScale(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Float32Flags, o + self._tab.Pos)
        return 0.0

    # TensorsDecodingOptions
    def ApplyExponentialOnBoxSize(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(24))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

    # TensorsDecodingOptions
    def SigmoidScore(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(26))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

def TensorsDecodingOptionsStart(builder):
    builder.StartObject(12)

def TensorsDecodingOptionsAddNumClasses(builder, numClasses):
    builder.PrependInt32Slot(0, numClasses, 0)

def TensorsDecodingOptionsAddNumBoxes(builder, numBoxes):
    builder.PrependInt32Slot(1, numBoxes, 0)

def TensorsDecodingOptionsAddNumCoords(builder, numCoords):
    builder.PrependInt32Slot(2, numCoords, 0)

def TensorsDecodingOptionsAddKeypointCoordOffset(builder, keypointCoordOffset):
    builder.PrependInt32Slot(3, keypointCoordOffset, 0)

def TensorsDecodingOptionsAddNumKeypoints(builder, numKeypoints):
    builder.PrependInt32Slot(4, numKeypoints, 0)

def TensorsDecodingOptionsAddNumValuesPerKeypoint(builder, numValuesPerKeypoint):
    builder.PrependInt32Slot(5, numValuesPerKeypoint, 0)

def TensorsDecodingOptionsAddXScale(builder, xScale):
    builder.PrependFloat32Slot(6, xScale, 0.0)

def TensorsDecodingOptionsAddYScale(builder, yScale):
    builder.PrependFloat32Slot(7, yScale, 0.0)

def TensorsDecodingOptionsAddWScale(builder, wScale):
    builder.PrependFloat32Slot(8, wScale, 0.0)

def TensorsDecodingOptionsAddHScale(builder, hScale):
    builder.PrependFloat32Slot(9, hScale, 0.0)

def TensorsDecodingOptionsAddApplyExponentialOnBoxSize(builder, applyExponentialOnBoxSize):
    builder.PrependBoolSlot(10, applyExponentialOnBoxSize, 0)

def TensorsDecodingOptionsAddSigmoidScore(builder, sigmoidScore):
    builder.PrependBoolSlot(11, sigmoidScore, 0)

def TensorsDecodingOptionsEnd(builder):
    return builder.EndObject()



class TensorsDecodingOptionsT(object):

    # TensorsDecodingOptionsT
    def __init__(self):
        self.numClasses = 0  # type: int
        self.numBoxes = 0  # type: int
        self.numCoords = 0  # type: int
        self.keypointCoordOffset = 0  # type: int
        self.numKeypoints = 0  # type: int
        self.numValuesPerKeypoint = 0  # type: int
        self.xScale = 0.0  # type: float
        self.yScale = 0.0  # type: float
        self.wScale = 0.0  # type: float
        self.hScale = 0.0  # type: float
        self.applyExponentialOnBoxSize = False  # type: bool
        self.sigmoidScore = False  # type: bool

    @classmethod
    def InitFromBuf(cls, buf, pos):
        tensorsDecodingOptions = TensorsDecodingOptions()
        tensorsDecodingOptions.Init(buf, pos)
        return cls.InitFromObj(tensorsDecodingOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, tensorsDecodingOptions):
        x = TensorsDecodingOptionsT()
        x._UnPack(tensorsDecodingOptions)
        return x

    # TensorsDecodingOptionsT
    def _UnPack(self, tensorsDecodingOptions):
        if tensorsDecodingOptions is None:
            return
        self.numClasses = tensorsDecodingOptions.NumClasses()
        self.numBoxes = tensorsDecodingOptions.NumBoxes()
        self.numCoords = tensorsDecodingOptions.NumCoords()
        self.keypointCoordOffset = tensorsDecodingOptions.KeypointCoordOffset()
        self.numKeypoints = tensorsDecodingOptions.NumKeypoints()
        self.numValuesPerKeypoint = tensorsDecodingOptions.NumValuesPerKeypoint()
        self.xScale = tensorsDecodingOptions.XScale()
        self.yScale = tensorsDecodingOptions.YScale()
        self.wScale = tensorsDecodingOptions.WScale()
        self.hScale = tensorsDecodingOptions.HScale()
        self.applyExponentialOnBoxSize = tensorsDecodingOptions.ApplyExponentialOnBoxSize()
        self.sigmoidScore = tensorsDecodingOptions.SigmoidScore()

    # TensorsDecodingOptionsT
    def Pack(self, builder):
        TensorsDecodingOptionsStart(builder)
        TensorsDecodingOptionsAddNumClasses(builder, self.numClasses)
        TensorsDecodingOptionsAddNumBoxes(builder, self.numBoxes)
        TensorsDecodingOptionsAddNumCoords(builder, self.numCoords)
        TensorsDecodingOptionsAddKeypointCoordOffset(builder, self.keypointCoordOffset)
        TensorsDecodingOptionsAddNumKeypoints(builder, self.numKeypoints)
        TensorsDecodingOptionsAddNumValuesPerKeypoint(builder, self.numValuesPerKeypoint)
        TensorsDecodingOptionsAddXScale(builder, self.xScale)
        TensorsDecodingOptionsAddYScale(builder, self.yScale)
        TensorsDecodingOptionsAddWScale(builder, self.wScale)
        TensorsDecodingOptionsAddHScale(builder, self.hScale)
        TensorsDecodingOptionsAddApplyExponentialOnBoxSize(builder, self.applyExponentialOnBoxSize)
        TensorsDecodingOptionsAddSigmoidScore(builder, self.sigmoidScore)
        tensorsDecodingOptions = TensorsDecodingOptionsEnd(builder)
        return tensorsDecodingOptions


class ObjectDetectorOptions(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = ObjectDetectorOptions()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsObjectDetectorOptions(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def ObjectDetectorOptionsBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x56\x30\x30\x31", size_prefixed=size_prefixed)

    # ObjectDetectorOptions
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # ObjectDetectorOptions
    def MinParserVersion(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # ObjectDetectorOptions
    def SsdAnchorsOptions(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = SsdAnchorsOptions()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # ObjectDetectorOptions
    def TensorsDecodingOptions(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            obj = TensorsDecodingOptions()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def ObjectDetectorOptionsStart(builder):
    builder.StartObject(3)

def ObjectDetectorOptionsAddMinParserVersion(builder, minParserVersion):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(minParserVersion), 0)

def ObjectDetectorOptionsAddSsdAnchorsOptions(builder, ssdAnchorsOptions):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(ssdAnchorsOptions), 0)

def ObjectDetectorOptionsAddTensorsDecodingOptions(builder, tensorsDecodingOptions):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(tensorsDecodingOptions), 0)

def ObjectDetectorOptionsEnd(builder):
    return builder.EndObject()


try:
    from typing import Optional
except:
    pass

class ObjectDetectorOptionsT(object):

    # ObjectDetectorOptionsT
    def __init__(self):
        self.minParserVersion = None  # type: str
        self.ssdAnchorsOptions = None  # type: Optional[SsdAnchorsOptionsT]
        self.tensorsDecodingOptions = None  # type: Optional[TensorsDecodingOptionsT]

    @classmethod
    def InitFromBuf(cls, buf, pos):
        objectDetectorOptions = ObjectDetectorOptions()
        objectDetectorOptions.Init(buf, pos)
        return cls.InitFromObj(objectDetectorOptions)

    @classmethod
    def InitFromPackedBuf(cls, buf, pos=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, pos)
        return cls.InitFromBuf(buf, pos+n)

    @classmethod
    def InitFromObj(cls, objectDetectorOptions):
        x = ObjectDetectorOptionsT()
        x._UnPack(objectDetectorOptions)
        return x

    # ObjectDetectorOptionsT
    def _UnPack(self, objectDetectorOptions):
        if objectDetectorOptions is None:
            return
        self.minParserVersion = objectDetectorOptions.MinParserVersion()
        if objectDetectorOptions.SsdAnchorsOptions() is not None:
            self.ssdAnchorsOptions = SsdAnchorsOptionsT.InitFromObj(objectDetectorOptions.SsdAnchorsOptions())
        if objectDetectorOptions.TensorsDecodingOptions() is not None:
            self.tensorsDecodingOptions = TensorsDecodingOptionsT.InitFromObj(objectDetectorOptions.TensorsDecodingOptions())

    # ObjectDetectorOptionsT
    def Pack(self, builder):
        if self.minParserVersion is not None:
            minParserVersion = builder.CreateString(self.minParserVersion)
        if self.ssdAnchorsOptions is not None:
            ssdAnchorsOptions = self.ssdAnchorsOptions.Pack(builder)
        if self.tensorsDecodingOptions is not None:
            tensorsDecodingOptions = self.tensorsDecodingOptions.Pack(builder)
        ObjectDetectorOptionsStart(builder)
        if self.minParserVersion is not None:
            ObjectDetectorOptionsAddMinParserVersion(builder, minParserVersion)
        if self.ssdAnchorsOptions is not None:
            ObjectDetectorOptionsAddSsdAnchorsOptions(builder, ssdAnchorsOptions)
        if self.tensorsDecodingOptions is not None:
            ObjectDetectorOptionsAddTensorsDecodingOptions(builder, tensorsDecodingOptions)
        objectDetectorOptions = ObjectDetectorOptionsEnd(builder)
        return objectDetectorOptions


