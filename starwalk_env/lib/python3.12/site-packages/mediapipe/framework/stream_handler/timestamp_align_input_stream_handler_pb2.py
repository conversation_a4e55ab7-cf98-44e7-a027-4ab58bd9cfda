# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/stream_handler/timestamp_align_input_stream_handler.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import mediapipe_options_pb2 as mediapipe_dot_framework_dot_mediapipe__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nMmediapipe/framework/stream_handler/timestamp_align_input_stream_handler.proto\x12\tmediapipe\x1a+mediapipe/framework/mediapipe_options.proto\"\xac\x01\n\'TimestampAlignInputStreamHandlerOptions\x12 \n\x18timestamp_base_tag_index\x18\x01 \x01(\t2_\n\x03\x65xt\x12\x1b.mediapipe.MediaPipeOptions\x18\x93\x8b\xd3Z \x01(\x0b\x32\x32.mediapipe.TimestampAlignInputStreamHandlerOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.stream_handler.timestamp_align_input_stream_handler_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_TIMESTAMPALIGNINPUTSTREAMHANDLEROPTIONS']._serialized_start=138
  _globals['_TIMESTAMPALIGNINPUTSTREAMHANDLEROPTIONS']._serialized_end=310
# @@protoc_insertion_point(module_scope)
