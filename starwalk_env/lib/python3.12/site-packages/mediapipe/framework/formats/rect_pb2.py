# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/rect.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&mediapipe/framework/formats/rect.proto\x12\tmediapipe\"o\n\x04Rect\x12\x10\n\x08x_center\x18\x01 \x02(\x05\x12\x10\n\x08y_center\x18\x02 \x02(\x05\x12\x0e\n\x06height\x18\x03 \x02(\x05\x12\r\n\x05width\x18\x04 \x02(\x05\x12\x13\n\x08rotation\x18\x05 \x01(\x02:\x01\x30\x12\x0f\n\x07rect_id\x18\x06 \x01(\x03\"y\n\x0eNormalizedRect\x12\x10\n\x08x_center\x18\x01 \x02(\x02\x12\x10\n\x08y_center\x18\x02 \x02(\x02\x12\x0e\n\x06height\x18\x03 \x02(\x02\x12\r\n\x05width\x18\x04 \x02(\x02\x12\x13\n\x08rotation\x18\x05 \x01(\x02:\x01\x30\x12\x0f\n\x07rect_id\x18\x06 \x01(\x03\x42/\n\"com.google.mediapipe.formats.protoB\tRectProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.rect_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.google.mediapipe.formats.protoB\tRectProto'
  _globals['_RECT']._serialized_start=53
  _globals['_RECT']._serialized_end=164
  _globals['_NORMALIZEDRECT']._serialized_start=166
  _globals['_NORMALIZEDRECT']._serialized_end=287
# @@protoc_insertion_point(module_scope)
