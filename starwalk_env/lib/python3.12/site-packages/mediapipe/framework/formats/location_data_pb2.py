# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/location_data.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework.formats.annotation import rasterization_pb2 as mediapipe_dot_framework_dot_formats_dot_annotation_dot_rasterization__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/mediapipe/framework/formats/location_data.proto\x12\tmediapipe\x1a:mediapipe/framework/formats/annotation/rasterization.proto\"\xd5\x05\n\x0cLocationData\x12.\n\x06\x66ormat\x18\x01 \x01(\x0e\x32\x1e.mediapipe.LocationData.Format\x12\x39\n\x0c\x62ounding_box\x18\x02 \x01(\x0b\x32#.mediapipe.LocationData.BoundingBox\x12J\n\x15relative_bounding_box\x18\x03 \x01(\x0b\x32+.mediapipe.LocationData.RelativeBoundingBox\x12\x30\n\x04mask\x18\x04 \x01(\x0b\x32\".mediapipe.LocationData.BinaryMask\x12\x44\n\x12relative_keypoints\x18\x05 \x03(\x0b\x32(.mediapipe.LocationData.RelativeKeypoint\x1aH\n\x0b\x42oundingBox\x12\x0c\n\x04xmin\x18\x01 \x01(\x05\x12\x0c\n\x04ymin\x18\x02 \x01(\x05\x12\r\n\x05width\x18\x03 \x01(\x05\x12\x0e\n\x06height\x18\x04 \x01(\x05\x1aP\n\x13RelativeBoundingBox\x12\x0c\n\x04xmin\x18\x01 \x01(\x02\x12\x0c\n\x04ymin\x18\x02 \x01(\x02\x12\r\n\x05width\x18\x03 \x01(\x02\x12\x0e\n\x06height\x18\x04 \x01(\x02\x1a\\\n\nBinaryMask\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12/\n\rrasterization\x18\x03 \x01(\x0b\x32\x18.mediapipe.Rasterization\x1aO\n\x10RelativeKeypoint\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x16\n\x0ekeypoint_label\x18\x03 \x01(\t\x12\r\n\x05score\x18\x04 \x01(\x02\"K\n\x06\x46ormat\x12\n\n\x06GLOBAL\x10\x00\x12\x10\n\x0c\x42OUNDING_BOX\x10\x01\x12\x19\n\x15RELATIVE_BOUNDING_BOX\x10\x02\x12\x08\n\x04MASK\x10\x03\x42\x37\n\"com.google.mediapipe.formats.protoB\x11LocationDataProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.location_data_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.google.mediapipe.formats.protoB\021LocationDataProto'
  _globals['_LOCATIONDATA']._serialized_start=123
  _globals['_LOCATIONDATA']._serialized_end=848
  _globals['_LOCATIONDATA_BOUNDINGBOX']._serialized_start=442
  _globals['_LOCATIONDATA_BOUNDINGBOX']._serialized_end=514
  _globals['_LOCATIONDATA_RELATIVEBOUNDINGBOX']._serialized_start=516
  _globals['_LOCATIONDATA_RELATIVEBOUNDINGBOX']._serialized_end=596
  _globals['_LOCATIONDATA_BINARYMASK']._serialized_start=598
  _globals['_LOCATIONDATA_BINARYMASK']._serialized_end=690
  _globals['_LOCATIONDATA_RELATIVEKEYPOINT']._serialized_start=692
  _globals['_LOCATIONDATA_RELATIVEKEYPOINT']._serialized_end=771
  _globals['_LOCATIONDATA_FORMAT']._serialized_start=773
  _globals['_LOCATIONDATA_FORMAT']._serialized_end=848
# @@protoc_insertion_point(module_scope)
