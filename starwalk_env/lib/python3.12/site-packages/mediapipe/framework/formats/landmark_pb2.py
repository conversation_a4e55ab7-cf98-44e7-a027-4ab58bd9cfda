# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/landmark.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*mediapipe/framework/formats/landmark.proto\x12\tmediapipe\"Q\n\x08Landmark\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\x12\x12\n\nvisibility\x18\x04 \x01(\x02\x12\x10\n\x08presence\x18\x05 \x01(\x02\"5\n\x0cLandmarkList\x12%\n\x08landmark\x18\x01 \x03(\x0b\x32\x13.mediapipe.Landmark\"H\n\x16LandmarkListCollection\x12.\n\rlandmark_list\x18\x01 \x03(\x0b\x32\x17.mediapipe.LandmarkList\"[\n\x12NormalizedLandmark\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\x12\x12\n\nvisibility\x18\x04 \x01(\x02\x12\x10\n\x08presence\x18\x05 \x01(\x02\"I\n\x16NormalizedLandmarkList\x12/\n\x08landmark\x18\x01 \x03(\x0b\x32\x1d.mediapipe.NormalizedLandmark\"\\\n NormalizedLandmarkListCollection\x12\x38\n\rlandmark_list\x18\x01 \x03(\x0b\x32!.mediapipe.NormalizedLandmarkListB3\n\"com.google.mediapipe.formats.protoB\rLandmarkProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.landmark_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.google.mediapipe.formats.protoB\rLandmarkProto'
  _globals['_LANDMARK']._serialized_start=57
  _globals['_LANDMARK']._serialized_end=138
  _globals['_LANDMARKLIST']._serialized_start=140
  _globals['_LANDMARKLIST']._serialized_end=193
  _globals['_LANDMARKLISTCOLLECTION']._serialized_start=195
  _globals['_LANDMARKLISTCOLLECTION']._serialized_end=267
  _globals['_NORMALIZEDLANDMARK']._serialized_start=269
  _globals['_NORMALIZEDLANDMARK']._serialized_end=360
  _globals['_NORMALIZEDLANDMARKLIST']._serialized_start=362
  _globals['_NORMALIZEDLANDMARKLIST']._serialized_end=435
  _globals['_NORMALIZEDLANDMARKLISTCOLLECTION']._serialized_start=437
  _globals['_NORMALIZEDLANDMARKLISTCOLLECTION']._serialized_end=529
# @@protoc_insertion_point(module_scope)
