# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/object_detection/anchor.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9mediapipe/framework/formats/object_detection/anchor.proto\x12\tmediapipe\"B\n\x06\x41nchor\x12\x10\n\x08x_center\x18\x01 \x02(\x02\x12\x10\n\x08y_center\x18\x02 \x02(\x02\x12\t\n\x01h\x18\x03 \x02(\x02\x12\t\n\x01w\x18\x04 \x02(\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.object_detection.anchor_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ANCHOR']._serialized_start=72
  _globals['_ANCHOR']._serialized_end=138
# @@protoc_insertion_point(module_scope)
