# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/image_format.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.mediapipe/framework/formats/image_format.proto\x12\tmediapipe\"\xc6\x01\n\x0bImageFormat\"\xb6\x01\n\x06\x46ormat\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x08\n\x04SRGB\x10\x01\x12\t\n\x05SRGBA\x10\x02\x12\t\n\x05GRAY8\x10\x03\x12\n\n\x06GRAY16\x10\x04\x12\r\n\tYCBCR420P\x10\x05\x12\x0f\n\x0bYCBCR420P10\x10\x06\x12\n\n\x06SRGB48\x10\x07\x12\x0b\n\x07SRGBA64\x10\x08\x12\x0b\n\x07VEC32F1\x10\t\x12\x0b\n\x07VEC32F2\x10\x0c\x12\x0b\n\x07VEC32F4\x10\r\x12\x08\n\x04LAB8\x10\n\x12\t\n\x05SBGRA\x10\x0b\x42\x36\n\"com.google.mediapipe.formats.protoB\x10ImageFormatProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.image_format_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.google.mediapipe.formats.protoB\020ImageFormatProto'
  _globals['_IMAGEFORMAT']._serialized_start=62
  _globals['_IMAGEFORMAT']._serialized_end=260
  _globals['_IMAGEFORMAT_FORMAT']._serialized_start=78
  _globals['_IMAGEFORMAT_FORMAT']._serialized_end=260
# @@protoc_insertion_point(module_scope)
