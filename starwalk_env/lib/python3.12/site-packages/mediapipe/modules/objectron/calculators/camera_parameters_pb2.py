# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/objectron/calculators/camera_parameters.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?mediapipe/modules/objectron/calculators/camera_parameters.proto\x12\tmediapipe\"\xe7\x03\n\x15\x43\x61meraParametersProto\x12 \n\x13height_above_ground\x18\x01 \x01(\x02:\x03\x31\x30\x30\x12\x1e\n\x0eportrait_width\x18\x02 \x01(\x02:\x06\x31.0103\x12\x1f\n\x0fportrait_height\x18\x03 \x01(\x02:\x06\x31.3435\x12\x62\n\x11image_orientation\x18\x04 \x01(\x0e\x32\x31.mediapipe.CameraParametersProto.ImageOrientation:\x14PORTRAIT_ORIENTATION\x12V\n\x0fprojection_mode\x18\x05 \x01(\x0e\x32/.mediapipe.CameraParametersProto.ProjectionMode:\x0cGROUND_PLANE\x12%\n\x18projection_sphere_radius\x18\x06 \x01(\x02:\x03\x31\x30\x30\"G\n\x10ImageOrientation\x12\x18\n\x14PORTRAIT_ORIENTATION\x10\x00\x12\x19\n\x15LANDSCAPE_ORIENTATION\x10\x01\"?\n\x0eProjectionMode\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x10\n\x0cGROUND_PLANE\x10\x01\x12\n\n\x06SPHERE\x10\x02')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.objectron.calculators.camera_parameters_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_CAMERAPARAMETERSPROTO']._serialized_start=79
  _globals['_CAMERAPARAMETERSPROTO']._serialized_end=566
  _globals['_CAMERAPARAMETERSPROTO_IMAGEORIENTATION']._serialized_start=430
  _globals['_CAMERAPARAMETERSPROTO_IMAGEORIENTATION']._serialized_end=501
  _globals['_CAMERAPARAMETERSPROTO_PROJECTIONMODE']._serialized_start=503
  _globals['_CAMERAPARAMETERSPROTO_PROJECTIONMODE']._serialized_end=566
# @@protoc_insertion_point(module_scope)
