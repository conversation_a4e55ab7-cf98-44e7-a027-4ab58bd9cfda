# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/objectron/calculators/frame_annotation_tracker_calculator.proto
# Protobuf Python Version: 4.25.5
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nQmediapipe/modules/objectron/calculators/frame_annotation_tracker_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xcf\x01\n\'FrameAnnotationTrackerCalculatorOptions\x12\x1a\n\riou_threshold\x18\x01 \x01(\x02:\x03\x30.5\x12\x11\n\timg_width\x18\x02 \x01(\x02\x12\x12\n\nimg_height\x18\x03 \x01(\x02\x32\x61\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xf5\x80\xf3\x8a\x01 \x01(\x0b\x32\x32.mediapipe.FrameAnnotationTrackerCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.objectron.calculators.frame_annotation_tracker_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_FRAMEANNOTATIONTRACKERCALCULATOROPTIONS']._serialized_start=135
  _globals['_FRAMEANNOTATIONTRACKERCALCULATOROPTIONS']._serialized_end=342
# @@protoc_insertion_point(module_scope)
