#!/usr/bin/env python3
"""
StarWalk Installation Test
Verifies that all components are properly installed and working
"""

import sys
import importlib
import platform

def test_python_version():
    """Test Python version compatibility"""
    version = sys.version_info
    print(f"🐍 Python Version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    else:
        print("✅ Python version is compatible")
        return True

def test_import(module_name, description=""):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description or module_name} - Available")
        return True
    except ImportError as e:
        print(f"❌ {description or module_name} - Not available")
        print(f"   Error: {e}")
        return False

def test_opencv():
    """Test OpenCV functionality"""
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__} - Available")
        
        # Test basic functionality
        test_image = cv2.imread("test.jpg")  # This will fail but shouldn't crash
        print("✅ OpenCV basic functions - Working")
        return True
    except Exception as e:
        print(f"❌ OpenCV - Error: {e}")
        return False

def test_mediapipe():
    """Test MediaPipe functionality"""
    try:
        import mediapipe as mp
        print(f"✅ MediaPipe - Available")
        
        # Test pose detection initialization
        mp_pose = mp.solutions.pose
        pose = mp_pose.Pose()
        print("✅ MediaPipe Pose - Working")
        return True
    except Exception as e:
        print(f"❌ MediaPipe - Error: {e}")
        return False

def test_starwalk_import():
    """Test StarWalk module import"""
    try:
        from starwalk import StarWalkAnalyzer
        analyzer = StarWalkAnalyzer()
        print("✅ StarWalk Analyzer - Available")
        return True
    except Exception as e:
        print(f"❌ StarWalk Analyzer - Error: {e}")
        return False

def test_ui_import():
    """Test UI module import"""
    try:
        import tkinter as tk
        print("✅ Tkinter (UI) - Available")
        
        # Test if starwalk_ui can be imported
        import starwalk_ui
        print("✅ StarWalk UI - Available")
        return True
    except Exception as e:
        print(f"❌ StarWalk UI - Error: {e}")
        return False

def print_system_info():
    """Print system information"""
    print("\n" + "="*50)
    print("           SYSTEM INFORMATION")
    print("="*50)
    print(f"🖥️  Operating System: {platform.system()} {platform.release()}")
    print(f"🏗️  Architecture: {platform.machine()}")
    print(f"🐍 Python Implementation: {platform.python_implementation()}")
    print(f"📍 Python Executable: {sys.executable}")

def main():
    """Main test function"""
    print("🧪 StarWalk Installation Test")
    print("="*50)
    
    # Print system info
    print_system_info()
    
    print("\n" + "="*50)
    print("           DEPENDENCY TESTS")
    print("="*50)
    
    tests_passed = 0
    total_tests = 0
    
    # Test Python version
    total_tests += 1
    if test_python_version():
        tests_passed += 1
    
    # Test core dependencies
    dependencies = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("matplotlib", "Matplotlib"),
        ("mediapipe", "MediaPipe"),
        ("tkinter", "Tkinter (UI)")
    ]
    
    for module, description in dependencies:
        total_tests += 1
        if test_import(module, description):
            tests_passed += 1
    
    # Test OpenCV functionality
    total_tests += 1
    if test_opencv():
        tests_passed += 1
    
    # Test MediaPipe functionality
    total_tests += 1
    if test_mediapipe():
        tests_passed += 1
    
    # Test StarWalk components
    total_tests += 1
    if test_starwalk_import():
        tests_passed += 1
    
    total_tests += 1
    if test_ui_import():
        tests_passed += 1
    
    # Print results
    print("\n" + "="*50)
    print("           TEST RESULTS")
    print("="*50)
    
    print(f"📊 Tests Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ StarWalk is ready to use!")
        print("\n🚀 Quick Start:")
        print("   • Run UI: python starwalk_ui.py")
        print("   • Run CLI: python starwalk.py dual_video.mp4 reference_video.mp4")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("❌ Please check the installation and resolve any issues")
        print("\n🔧 Troubleshooting:")
        print("   • Ensure virtual environment is activated")
        print("   • Run: pip install -r requirements.txt")
        print("   • Check Python version (3.8+ required)")
    
    print("\n📚 For more help, see INSTALLATION_GUIDE.md")

if __name__ == "__main__":
    main()
