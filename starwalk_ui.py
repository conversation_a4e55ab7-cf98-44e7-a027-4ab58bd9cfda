#!/usr/bin/env python3
"""
StarWalk UI - Simple drag-and-drop interface for dual person walk analysis
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
import queue
import logging
import traceback
from datetime import datetime

# Setup comprehensive logging
def setup_logging():
    """Setup logging to both file and console"""
    log_filename = f"starwalk_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    # Create logger
    logger = logging.getLogger('StarWalk')
    logger.setLevel(logging.DEBUG)

    # Create file handler
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(logging.DEBUG)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger, log_filename

# Initialize logging
logger, log_file = setup_logging()
logger.info("StarWalk UI starting up...")

# Import our StarWalk analyzer with error handling
try:
    logger.info("Importing StarWalk analyzer...")
    from starwalk import StarWalkAnalyzer
    logger.info("StarWalk analyzer imported successfully")
except ImportError as e:
    logger.error(f"Failed to import StarWalk analyzer: {e}")
    messagebox.showerror("Import Error", f"Could not import StarWalk analyzer: {e}\nPlease ensure starwalk.py is in the same directory.")
    sys.exit(1)
except Exception as e:
    logger.error(f"Unexpected error importing StarWalk analyzer: {e}")
    logger.error(traceback.format_exc())
    messagebox.showerror("Import Error", f"Unexpected error: {e}")
    sys.exit(1)

class StarWalkUI:
    def __init__(self, root):
        try:
            logger.info("Initializing StarWalk UI...")
            self.root = root
            self.root.title("StarWalk - Dual Person Walk Analysis")
            self.root.geometry("800x700")
            self.root.configure(bg='#f0f0f0')

            # Variables
            self.dual_video_path = tk.StringVar()
            self.reference_video_path = tk.StringVar()

            # Initialize analyzer with error handling
            logger.info("Creating StarWalk analyzer instance...")
            self.analyzer = StarWalkAnalyzer()
            logger.info("StarWalk analyzer created successfully")

            self.analysis_running = False

            # Queue for thread communication
            self.message_queue = queue.Queue()

            # Setup error handling for UI
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            logger.info("Setting up UI components...")
            self.setup_ui()
            logger.info("UI setup completed")

            self.check_queue()
            logger.info("StarWalk UI initialization completed successfully")

        except Exception as e:
            logger.error(f"Error during UI initialization: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("Initialization Error", f"Failed to initialize UI: {e}")
            raise
    
    def setup_ui(self):
        """Setup the user interface with Analytics Vidhya design aesthetic"""

        # Analytics Vidhya Color Scheme
        self.colors = {
            'primary': '#1a237e',      # Deep blue
            'secondary': '#3f51b5',    # Medium blue
            'accent': '#ff6f00',       # Orange accent
            'success': '#4caf50',      # Green
            'warning': '#ff9800',      # Orange
            'error': '#f44336',        # Red
            'background': '#fafafa',   # Light gray
            'surface': '#ffffff',      # White
            'text_primary': '#212121', # Dark gray
            'text_secondary': '#757575' # Medium gray
        }

        # Update root background
        self.root.configure(bg=self.colors['background'])

        # Header Section with gradient-like effect
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=120)
        header_frame.pack(fill='x', pady=0)
        header_frame.pack_propagate(False)

        # Title with modern styling
        title_container = tk.Frame(header_frame, bg=self.colors['primary'])
        title_container.pack(expand=True, fill='both')

        title_label = tk.Label(
            title_container,
            text="⚡ StarWalk AI",
            font=("Segoe UI", 28, "bold"),
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack(pady=(20, 5))

        subtitle_label = tk.Label(
            title_container,
            text="Advanced Dual Person Gait Analysis • Powered by Computer Vision",
            font=("Segoe UI", 11),
            bg=self.colors['primary'],
            fg='#e3f2fd'
        )
        subtitle_label.pack()
        
        # Main content container
        main_container = tk.Frame(self.root, bg=self.colors['background'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Video selection section
        video_section = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        video_section.pack(fill='x', pady=(0, 15))

        # Section header
        section_header = tk.Frame(video_section, bg=self.colors['surface'])
        section_header.pack(fill='x', padx=25, pady=(20, 15))

        tk.Label(
            section_header,
            text="📹 Video Input",
            font=("Segoe UI", 16, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_primary']
        ).pack(side='left')

        tk.Label(
            section_header,
            text="Upload your videos for AI-powered gait analysis",
            font=("Segoe UI", 10),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack(side='left', padx=(10, 0))

        # Dual video input
        dual_container = tk.Frame(video_section, bg=self.colors['surface'])
        dual_container.pack(fill='x', padx=25, pady=(0, 15))

        dual_label_frame = tk.Frame(dual_container, bg=self.colors['surface'])
        dual_label_frame.pack(fill='x', pady=(0, 8))

        tk.Label(
            dual_label_frame,
            text="👥 Dual Person Video",
            font=("Segoe UI", 12, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_primary']
        ).pack(side='left')

        tk.Label(
            dual_label_frame,
            text="• 16:9 aspect ratio recommended • Two people walking side by side",
            font=("Segoe UI", 9),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack(side='left', padx=(10, 0))

        dual_input_frame = tk.Frame(dual_container, bg=self.colors['surface'])
        dual_input_frame.pack(fill='x')

        self.dual_entry = tk.Entry(
            dual_input_frame,
            textvariable=self.dual_video_path,
            font=("Segoe UI", 11),
            state='readonly',
            bg='#f8f9fa',
            fg=self.colors['text_primary'],
            relief='flat',
            bd=1,
            highlightthickness=1,
            highlightcolor=self.colors['secondary']
        )
        self.dual_entry.pack(side='left', fill='x', expand=True, ipady=8)

        dual_browse_btn = tk.Button(
            dual_input_frame,
            text="📁 Browse",
            command=self.browse_dual_video,
            bg=self.colors['secondary'],
            fg='white',
            font=("Segoe UI", 10, "bold"),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        dual_browse_btn.pack(side='right', padx=(10, 0))

        # Hover effects for browse button
        def on_enter_dual(e):
            dual_browse_btn.config(bg=self.colors['primary'])
        def on_leave_dual(e):
            dual_browse_btn.config(bg=self.colors['secondary'])

        dual_browse_btn.bind("<Enter>", on_enter_dual)
        dual_browse_btn.bind("<Leave>", on_leave_dual)
        
        # Reference video input
        ref_container = tk.Frame(video_section, bg=self.colors['surface'])
        ref_container.pack(fill='x', padx=25, pady=(0, 20))

        ref_label_frame = tk.Frame(ref_container, bg=self.colors['surface'])
        ref_label_frame.pack(fill='x', pady=(0, 8))

        tk.Label(
            ref_label_frame,
            text="🎯 Reference Video",
            font=("Segoe UI", 12, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_primary']
        ).pack(side='left')

        tk.Label(
            ref_label_frame,
            text="• Any aspect ratio • Single person walking (ideal gait pattern)",
            font=("Segoe UI", 9),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack(side='left', padx=(10, 0))

        ref_input_frame = tk.Frame(ref_container, bg=self.colors['surface'])
        ref_input_frame.pack(fill='x')

        self.ref_entry = tk.Entry(
            ref_input_frame,
            textvariable=self.reference_video_path,
            font=("Segoe UI", 11),
            state='readonly',
            bg='#f8f9fa',
            fg=self.colors['text_primary'],
            relief='flat',
            bd=1,
            highlightthickness=1,
            highlightcolor=self.colors['secondary']
        )
        self.ref_entry.pack(side='left', fill='x', expand=True, ipady=8)

        ref_browse_btn = tk.Button(
            ref_input_frame,
            text="📁 Browse",
            command=self.browse_reference_video,
            bg=self.colors['secondary'],
            fg='white',
            font=("Segoe UI", 10, "bold"),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        ref_browse_btn.pack(side='right', padx=(10, 0))

        # Hover effects for reference browse button
        def on_enter_ref(e):
            ref_browse_btn.config(bg=self.colors['primary'])
        def on_leave_ref(e):
            ref_browse_btn.config(bg=self.colors['secondary'])

        ref_browse_btn.bind("<Enter>", on_enter_ref)
        ref_browse_btn.bind("<Leave>", on_leave_ref)
        
        # Analysis options section
        options_section = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        options_section.pack(fill='x', pady=(0, 15))

        # Options header
        options_header = tk.Frame(options_section, bg=self.colors['surface'])
        options_header.pack(fill='x', padx=25, pady=(20, 15))

        tk.Label(
            options_header,
            text="⚙️ Analysis Configuration",
            font=("Segoe UI", 16, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_primary']
        ).pack(side='left')

        tk.Label(
            options_header,
            text="Configure output options for your analysis",
            font=("Segoe UI", 10),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack(side='left', padx=(10, 0))

        # Options container
        options_container = tk.Frame(options_section, bg=self.colors['surface'])
        options_container.pack(fill='x', padx=25, pady=(0, 20))

        self.save_plot_var = tk.BooleanVar(value=True)
        self.save_video_var = tk.BooleanVar(value=True)

        # Custom styled checkboxes
        plot_frame = tk.Frame(options_container, bg=self.colors['surface'])
        plot_frame.pack(fill='x', pady=(0, 10))

        plot_check = tk.Checkbutton(
            plot_frame,
            text="📊 Generate Analysis Charts & Metrics Visualization",
            variable=self.save_plot_var,
            font=("Segoe UI", 11),
            bg=self.colors['surface'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['accent'],
            activebackground=self.colors['surface'],
            relief='flat',
            bd=0
        )
        plot_check.pack(anchor='w')

        video_frame = tk.Frame(options_container, bg=self.colors['surface'])
        video_frame.pack(fill='x')

        video_check = tk.Checkbutton(
            video_frame,
            text="🎥 Create Side-by-Side Comparison Video",
            variable=self.save_video_var,
            font=("Segoe UI", 11),
            bg=self.colors['surface'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['accent'],
            activebackground=self.colors['surface'],
            relief='flat',
            bd=0
        )
        video_check.pack(anchor='w')
        
        # Control section
        control_section = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        control_section.pack(fill='x', pady=(0, 15))

        # Control buttons container
        control_container = tk.Frame(control_section, bg=self.colors['surface'])
        control_container.pack(fill='x', padx=25, pady=20)

        # Main action button
        self.analyze_button = tk.Button(
            control_container,
            text="🚀 Start AI Analysis",
            command=self.start_analysis,
            bg=self.colors['accent'],
            fg='white',
            font=("Segoe UI", 14, "bold"),
            relief='flat',
            bd=0,
            padx=40,
            pady=12,
            cursor='hand2'
        )
        self.analyze_button.pack(side='left')

        # Secondary buttons
        secondary_buttons_frame = tk.Frame(control_container, bg=self.colors['surface'])
        secondary_buttons_frame.pack(side='right')

        output_btn = tk.Button(
            secondary_buttons_frame,
            text="📁 Open Results",
            command=self.open_output_folder,
            bg=self.colors['text_secondary'],
            fg='white',
            font=("Segoe UI", 11),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        output_btn.pack(side='left', padx=(0, 10))

        clear_btn = tk.Button(
            secondary_buttons_frame,
            text="🗑️ Clear All",
            command=self.clear_all,
            bg='#e0e0e0',
            fg=self.colors['text_primary'],
            font=("Segoe UI", 11),
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        clear_btn.pack(side='left')

        # Hover effects for buttons
        def on_enter_analyze(e):
            self.analyze_button.config(bg='#e65100')
        def on_leave_analyze(e):
            self.analyze_button.config(bg=self.colors['accent'])

        def on_enter_output(e):
            output_btn.config(bg='#424242')
        def on_leave_output(e):
            output_btn.config(bg=self.colors['text_secondary'])

        def on_enter_clear(e):
            clear_btn.config(bg='#d0d0d0')
        def on_leave_clear(e):
            clear_btn.config(bg='#e0e0e0')

        self.analyze_button.bind("<Enter>", on_enter_analyze)
        self.analyze_button.bind("<Leave>", on_leave_analyze)
        output_btn.bind("<Enter>", on_enter_output)
        output_btn.bind("<Leave>", on_leave_output)
        clear_btn.bind("<Enter>", on_enter_clear)
        clear_btn.bind("<Leave>", on_leave_clear)
        
        # Progress section
        progress_section = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        progress_section.pack(fill='x', pady=(0, 15))

        progress_container = tk.Frame(progress_section, bg=self.colors['surface'])
        progress_container.pack(fill='x', padx=25, pady=20)

        self.progress_var = tk.StringVar(value="Ready to analyze • Upload videos to begin")
        self.progress_label = tk.Label(
            progress_container,
            textvariable=self.progress_var,
            font=("Segoe UI", 11),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        )
        self.progress_label.pack(pady=(0, 10))

        # Custom styled progress bar
        progress_frame = tk.Frame(progress_container, bg=self.colors['surface'])
        progress_frame.pack(fill='x')

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            mode='indeterminate',
            length=500,
            style='Custom.Horizontal.TProgressbar'
        )
        self.progress_bar.pack(fill='x')

        # Configure progress bar style
        style = ttk.Style()
        style.configure('Custom.Horizontal.TProgressbar',
                       background=self.colors['accent'],
                       troughcolor='#e0e0e0',
                       borderwidth=0,
                       lightcolor=self.colors['accent'],
                       darkcolor=self.colors['accent'])

        # Results section
        results_section = tk.Frame(main_container, bg=self.colors['surface'], relief='flat', bd=0)
        results_section.pack(fill='both', expand=True)

        # Results header with score display
        results_header = tk.Frame(results_section, bg=self.colors['surface'])
        results_header.pack(fill='x', padx=25, pady=(20, 10))

        tk.Label(
            results_header,
            text="📊 Analysis Results",
            font=("Segoe UI", 16, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_primary']
        ).pack(side='left')

        # Score display frame
        self.score_frame = tk.Frame(results_header, bg=self.colors['surface'])
        self.score_frame.pack(side='right')

        # Results text area with modern styling
        results_container = tk.Frame(results_section, bg=self.colors['surface'])
        results_container.pack(fill='both', expand=True, padx=25, pady=(0, 20))

        self.results_text = scrolledtext.ScrolledText(
            results_container,
            height=14,
            font=("JetBrains Mono", 10),
            bg='#1e1e1e',
            fg='#d4d4d4',
            insertbackground='#ffffff',
            selectbackground=self.colors['secondary'],
            selectforeground='white',
            relief='flat',
            bd=0,
            padx=15,
            pady=15
        )
        self.results_text.pack(fill='both', expand=True)
        
        # Add drag and drop functionality
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        try:
            from tkinterdnd2 import DND_FILES, TkinterDnD

            # Test if tkinterdnd2 is properly installed and working
            test_root = TkinterDnD.Tk()
            test_root.destroy()

            # If we get here, tkinterdnd2 is working
            print("✅ Drag and drop functionality enabled")

            # Enable drag and drop on entry widgets
            self.dual_entry.drop_target_register(DND_FILES)
            self.dual_entry.dnd_bind('<<Drop>>', self.drop_dual_video)

            self.ref_entry.drop_target_register(DND_FILES)
            self.ref_entry.dnd_bind('<<Drop>>', self.drop_reference_video)

        except (ImportError, Exception) as e:
            # Drag and drop not available, continue without it
            print("ℹ️  Drag and drop not available, using browse buttons only")
            print(f"   Reason: {e}")
            pass
    
    def drop_dual_video(self, event):
        """Handle drag and drop for dual video"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.dual_video_path.set(files[0])
    
    def drop_reference_video(self, event):
        """Handle drag and drop for reference video"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.reference_video_path.set(files[0])
    
    def browse_dual_video(self):
        """Browse for dual person video"""
        filename = filedialog.askopenfilename(
            title="Select Dual Person Video",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.dual_video_path.set(filename)
    
    def browse_reference_video(self):
        """Browse for reference video"""
        filename = filedialog.askopenfilename(
            title="Select Reference Video",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.reference_video_path.set(filename)

    def validate_inputs(self):
        """Validate input files"""
        dual_path = self.dual_video_path.get()
        ref_path = self.reference_video_path.get()

        if not dual_path:
            messagebox.showerror("Error", "Please select a dual person video")
            return False

        if not ref_path:
            messagebox.showerror("Error", "Please select a reference video")
            return False

        if not os.path.exists(dual_path):
            messagebox.showerror("Error", f"Dual video file not found: {dual_path}")
            return False

        if not os.path.exists(ref_path):
            messagebox.showerror("Error", f"Reference video file not found: {ref_path}")
            return False

        return True

    def start_analysis(self):
        """Start the analysis in a separate thread"""
        if self.analysis_running:
            messagebox.showwarning("Warning", "Analysis is already running!")
            return

        if not self.validate_inputs():
            return

        # Disable the analyze button and start progress
        self.analyze_button.config(state='disabled')
        self.progress_bar.start()
        self.analysis_running = True
        self.progress_var.set("Starting analysis...")

        # Clear previous results
        self.results_text.delete(1.0, tk.END)

        # Start analysis in separate thread
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def run_analysis(self):
        """Run the analysis (called in separate thread)"""
        try:
            dual_path = self.dual_video_path.get()
            ref_path = self.reference_video_path.get()

            logger.info(f"Starting analysis with dual video: {dual_path}")
            logger.info(f"Reference video: {ref_path}")

            # Update progress
            self.message_queue.put(("progress", "Processing videos..."))

            # Perform analysis with detailed logging
            logger.info("Beginning video analysis...")
            results = self.analyzer.compare_dual_walk(dual_path, ref_path)
            logger.info("Video analysis completed successfully")

            # Update progress
            self.message_queue.put(("progress", "Generating results..."))

            # Format results for display
            logger.info("Formatting results for display...")
            results_text = self.format_results(results)
            self.message_queue.put(("results", results_text))
            logger.info("Results formatted successfully")

            # Save outputs if requested
            if self.save_plot_var.get():
                logger.info("Saving analysis plot...")
                self.message_queue.put(("progress", "Saving analysis plot..."))
                self.analyzer.plot_frame_analysis(results, "starwalk_analysis.png")
                self.message_queue.put(("info", "Analysis plot saved as: starwalk_analysis.png"))
                logger.info("Analysis plot saved successfully")

            if self.save_video_var.get():
                logger.info("Saving comparison video...")
                self.message_queue.put(("progress", "Saving comparison video..."))
                self.analyzer.save_comparison_video(results, "starwalk_comparison.mp4")
                self.message_queue.put(("info", "Comparison video saved as: starwalk_comparison.mp4"))
                logger.info("Comparison video saved successfully")

            self.message_queue.put(("complete", "Analysis completed successfully!"))
            logger.info("Analysis workflow completed successfully")

        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            logger.error(f"Analysis failed: {e}")
            logger.error(traceback.format_exc())
            self.message_queue.put(("error", error_msg))

    def format_results(self, results):
        """Format analysis results for display with score highlighting"""
        person1_metrics = results['person1_metrics']
        person2_metrics = results['person2_metrics']
        winner = results['winner']

        # Update score display in header
        self.update_score_display(person1_metrics.overall_score, person2_metrics.overall_score, winner)

        output = []
        output.append("=" * 70)
        output.append("              ⚡ STARWALK AI ANALYSIS RESULTS")
        output.append("=" * 70)
        output.append("")
        output.append(f"🏆 WINNER: {winner}")
        output.append(f"   Score Gap: {abs(person1_metrics.overall_score - person2_metrics.overall_score):.3f}")
        output.append("")
        output.append("📊 WEIGHTED METRICS COMPARISON:")
        output.append(f"{'Metric':<25} {'Person 1':<12} {'Person 2':<12} {'Weight':<8} {'Better':<10}")
        output.append("-" * 75)

        metrics_comparison = [
            ("Similarity Score", person1_metrics.similarity_score, person2_metrics.similarity_score, "40%"),
            ("Stride Consistency", person1_metrics.stride_consistency, person2_metrics.stride_consistency, "30%"),
            ("Posture Stability", person1_metrics.posture_stability, person2_metrics.posture_stability, "20%"),
            ("Arm Coordination", person1_metrics.arm_swing_coordination, person2_metrics.arm_swing_coordination, "10%"),
            ("OVERALL SCORE", person1_metrics.overall_score, person2_metrics.overall_score, "100%")
        ]

        for metric_name, p1_score, p2_score, weight in metrics_comparison:
            better = "Person 1" if p1_score > p2_score else "Person 2"
            if metric_name == "OVERALL SCORE":
                output.append("-" * 75)
            output.append(f"{metric_name:<25} {p1_score:<12.3f} {p2_score:<12.3f} {weight:<8} {better:<10}")

        output.append("")
        output.append("📈 PERFORMANCE GRADES:")
        output.append(f"Person 1 (Left):  {self.analyzer.get_performance_grade(person1_metrics.overall_score)}")
        output.append(f"Person 2 (Right): {self.analyzer.get_performance_grade(person2_metrics.overall_score)}")

        output.append("")
        output.append("📋 DETAILED BREAKDOWN:")
        output.append(f"Person 1 (Left):")
        output.append(f"  • Similarity to Reference: {person1_metrics.similarity_score:.3f} (40% weight)")
        output.append(f"  • Stride Consistency: {person1_metrics.stride_consistency:.3f} (30% weight)")
        output.append(f"  • Posture Stability: {person1_metrics.posture_stability:.3f} (20% weight)")
        output.append(f"  • Arm Coordination: {person1_metrics.arm_swing_coordination:.3f} (10% weight)")
        output.append(f"  • FINAL SCORE: {person1_metrics.overall_score:.3f}")
        output.append("")
        output.append(f"Person 2 (Right):")
        output.append(f"  • Similarity to Reference: {person2_metrics.similarity_score:.3f} (40% weight)")
        output.append(f"  • Stride Consistency: {person2_metrics.stride_consistency:.3f} (30% weight)")
        output.append(f"  • Posture Stability: {person2_metrics.posture_stability:.3f} (20% weight)")
        output.append(f"  • Arm Coordination: {person2_metrics.arm_swing_coordination:.3f} (10% weight)")
        output.append(f"  • FINAL SCORE: {person2_metrics.overall_score:.3f}")

        return "\n".join(output)

    def update_score_display(self, score1: float, score2: float, winner: str):
        """Update the score display in the results header"""
        # Clear existing score widgets
        for widget in self.score_frame.winfo_children():
            widget.destroy()

        # Person 1 score
        score1_frame = tk.Frame(self.score_frame, bg=self.colors['surface'])
        score1_frame.pack(side='left', padx=(0, 20))

        tk.Label(
            score1_frame,
            text="Person 1",
            font=("Segoe UI", 10),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack()

        score1_color = self.colors['success'] if "Person 1" in winner else self.colors['text_secondary']
        tk.Label(
            score1_frame,
            text=f"{score1:.3f}",
            font=("Segoe UI", 16, "bold"),
            bg=self.colors['surface'],
            fg=score1_color
        ).pack()

        # VS separator
        tk.Label(
            self.score_frame,
            text="VS",
            font=("Segoe UI", 12, "bold"),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack(side='left', padx=10)

        # Person 2 score
        score2_frame = tk.Frame(self.score_frame, bg=self.colors['surface'])
        score2_frame.pack(side='left', padx=(20, 0))

        tk.Label(
            score2_frame,
            text="Person 2",
            font=("Segoe UI", 10),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary']
        ).pack()

        score2_color = self.colors['success'] if "Person 2" in winner else self.colors['text_secondary']
        tk.Label(
            score2_frame,
            text=f"{score2:.3f}",
            font=("Segoe UI", 16, "bold"),
            bg=self.colors['surface'],
            fg=score2_color
        ).pack()

    def check_queue(self):
        """Check for messages from analysis thread"""
        try:
            while True:
                message_type, message = self.message_queue.get_nowait()

                if message_type == "progress":
                    self.progress_var.set(message)
                elif message_type == "results":
                    self.results_text.insert(tk.END, message)
                    self.results_text.see(tk.END)
                elif message_type == "info":
                    self.results_text.insert(tk.END, f"\n{message}\n")
                    self.results_text.see(tk.END)
                elif message_type == "error":
                    self.progress_bar.stop()
                    self.analyze_button.config(state='normal')
                    self.analysis_running = False
                    self.progress_var.set("Error occurred")
                    self.results_text.insert(tk.END, f"\n❌ {message}\n")
                    self.results_text.see(tk.END)
                    messagebox.showerror("Analysis Error", message)
                elif message_type == "complete":
                    self.progress_bar.stop()
                    self.analyze_button.config(state='normal')
                    self.analysis_running = False
                    self.progress_var.set("Analysis completed!")
                    self.results_text.insert(tk.END, f"\n✅ {message}\n")
                    self.results_text.see(tk.END)
                    messagebox.showinfo("Success", message)

        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.check_queue)

    def open_output_folder(self):
        """Open the output folder"""
        import subprocess
        import platform

        current_dir = os.getcwd()

        if platform.system() == "Windows":
            subprocess.Popen(f'explorer "{current_dir}"')
        elif platform.system() == "Darwin":  # macOS
            subprocess.Popen(["open", current_dir])
        else:  # Linux
            subprocess.Popen(["xdg-open", current_dir])

    def clear_all(self):
        """Clear all inputs and results"""
        logger.info("Clearing all inputs and results")
        self.dual_video_path.set("")
        self.reference_video_path.set("")
        self.results_text.delete(1.0, tk.END)
        self.progress_var.set("Ready to analyze")

    def on_closing(self):
        """Handle application closing"""
        try:
            logger.info("Application closing...")
            if self.analysis_running:
                logger.warning("Analysis is running, forcing close...")
                messagebox.showwarning("Warning", "Analysis is running. Forcing close...")

            logger.info("Destroying UI...")
            self.root.destroy()
            logger.info("Application closed successfully")

        except Exception as e:
            logger.error(f"Error during application close: {e}")
            logger.error(traceback.format_exc())


def main():
    """Main function to run the UI"""
    try:
        logger.info("Starting main function...")

        # Create root window with error handling
        logger.info("Creating root window...")
        root = tk.Tk()
        logger.info("Root window created successfully")

        # Initialize UI
        logger.info("Initializing StarWalk UI...")
        app = StarWalkUI(root)
        logger.info("StarWalk UI initialized successfully")

        # Center the window
        logger.info("Centering window...")
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
        y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
        root.geometry(f"+{x}+{y}")
        logger.info(f"Window centered at position ({x}, {y})")

        # Show log file location
        logger.info(f"Log file created: {log_file}")
        print(f"📝 Log file: {log_file}")

        # Start main loop
        logger.info("Starting main event loop...")
        root.mainloop()
        logger.info("Main event loop ended")

    except Exception as e:
        logger.error(f"Critical error in main function: {e}")
        logger.error(traceback.format_exc())
        messagebox.showerror("Critical Error", f"Failed to start application: {e}\n\nCheck log file: {log_file}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\n🛑 Application interrupted by user")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        logger.error(traceback.format_exc())
        print(f"\n❌ Critical error: {e}")
        print(f"📝 Check log file: {log_file}")
        sys.exit(1)
