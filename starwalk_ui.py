#!/usr/bin/env python3
"""
StarWalk UI - Simple drag-and-drop interface for dual person walk analysis
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path
import queue
import time

# Import our StarWalk analyzer
try:
    from starwalk import StarWalkAnalyzer
except ImportError:
    messagebox.showerror("Import Error", "Could not import StarWalk analyzer. Please ensure starwalk.py is in the same directory.")
    sys.exit(1)

class StarWalkUI:
    def __init__(self, root):
        self.root = root
        self.root.title("StarWalk - Dual Person Walk Analysis")
        self.root.geometry("800x700")
        self.root.configure(bg='#f0f0f0')
        
        # Variables
        self.dual_video_path = tk.StringVar()
        self.reference_video_path = tk.StringVar()
        self.analyzer = StarWalkAnalyzer()
        self.analysis_running = False
        
        # Queue for thread communication
        self.message_queue = queue.Queue()
        
        self.setup_ui()
        self.check_queue()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame, 
            text="🌟 StarWalk Analysis", 
            font=("Arial", 24, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Dual Person Walk Comparison System",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        subtitle_label.pack()
        
        # Video selection frame
        video_frame = tk.LabelFrame(
            self.root, 
            text="📹 Video Selection", 
            font=("Arial", 14, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50',
            padx=20,
            pady=15
        )
        video_frame.pack(fill='x', padx=20, pady=10)
        
        # Dual video selection
        dual_frame = tk.Frame(video_frame, bg='#f0f0f0')
        dual_frame.pack(fill='x', pady=5)
        
        tk.Label(
            dual_frame, 
            text="Dual Person Video (16:9):", 
            font=("Arial", 11, "bold"),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        dual_entry_frame = tk.Frame(dual_frame, bg='#f0f0f0')
        dual_entry_frame.pack(fill='x', pady=5)
        
        self.dual_entry = tk.Entry(
            dual_entry_frame, 
            textvariable=self.dual_video_path,
            font=("Arial", 10),
            state='readonly'
        )
        self.dual_entry.pack(side='left', fill='x', expand=True)
        
        tk.Button(
            dual_entry_frame,
            text="Browse",
            command=self.browse_dual_video,
            bg='#3498db',
            fg='white',
            font=("Arial", 10),
            padx=15
        ).pack(side='right', padx=(10, 0))
        
        # Reference video selection
        ref_frame = tk.Frame(video_frame, bg='#f0f0f0')
        ref_frame.pack(fill='x', pady=5)
        
        tk.Label(
            ref_frame, 
            text="Reference Video (any ratio):", 
            font=("Arial", 11, "bold"),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        ref_entry_frame = tk.Frame(ref_frame, bg='#f0f0f0')
        ref_entry_frame.pack(fill='x', pady=5)
        
        self.ref_entry = tk.Entry(
            ref_entry_frame, 
            textvariable=self.reference_video_path,
            font=("Arial", 10),
            state='readonly'
        )
        self.ref_entry.pack(side='left', fill='x', expand=True)
        
        tk.Button(
            ref_entry_frame,
            text="Browse",
            command=self.browse_reference_video,
            bg='#3498db',
            fg='white',
            font=("Arial", 10),
            padx=15
        ).pack(side='right', padx=(10, 0))
        
        # Options frame
        options_frame = tk.LabelFrame(
            self.root,
            text="⚙️ Analysis Options",
            font=("Arial", 14, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50',
            padx=20,
            pady=15
        )
        options_frame.pack(fill='x', padx=20, pady=10)
        
        self.save_plot_var = tk.BooleanVar(value=True)
        self.save_video_var = tk.BooleanVar(value=True)
        
        tk.Checkbutton(
            options_frame,
            text="Save analysis plot",
            variable=self.save_plot_var,
            font=("Arial", 11),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        tk.Checkbutton(
            options_frame,
            text="Save comparison video",
            variable=self.save_video_var,
            font=("Arial", 11),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        # Control buttons
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=20)
        
        self.analyze_button = tk.Button(
            button_frame,
            text="🚀 Start Analysis",
            command=self.start_analysis,
            bg='#27ae60',
            fg='white',
            font=("Arial", 14, "bold"),
            padx=30,
            pady=10
        )
        self.analyze_button.pack(side='left', padx=10)
        
        tk.Button(
            button_frame,
            text="🗂️ Open Output Folder",
            command=self.open_output_folder,
            bg='#f39c12',
            fg='white',
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side='left', padx=10)
        
        tk.Button(
            button_frame,
            text="❌ Clear",
            command=self.clear_all,
            bg='#e74c3c',
            fg='white',
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side='left', padx=10)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready to analyze")
        self.progress_label = tk.Label(
            self.root,
            textvariable=self.progress_var,
            font=("Arial", 11),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(
            self.root,
            mode='indeterminate',
            length=400
        )
        self.progress_bar.pack(pady=5)
        
        # Results text area
        results_frame = tk.LabelFrame(
            self.root,
            text="📊 Analysis Results",
            font=("Arial", 14, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.results_text = scrolledtext.ScrolledText(
            results_frame,
            height=12,
            font=("Consolas", 10),
            bg='#2c3e50',
            fg='#ecf0f1',
            insertbackground='white'
        )
        self.results_text.pack(fill='both', expand=True)
        
        # Add drag and drop functionality
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        try:
            from tkinterdnd2 import DND_FILES, TkinterDnD
            
            # Convert root to TkinterDnD
            self.root = TkinterDnD.Tk()
            self.root.title("StarWalk - Dual Person Walk Analysis")
            self.root.geometry("800x700")
            self.root.configure(bg='#f0f0f0')
            
            # Enable drag and drop on entry widgets
            self.dual_entry.drop_target_register(DND_FILES)
            self.dual_entry.dnd_bind('<<Drop>>', self.drop_dual_video)
            
            self.ref_entry.drop_target_register(DND_FILES)
            self.ref_entry.dnd_bind('<<Drop>>', self.drop_reference_video)
            
        except ImportError:
            # Drag and drop not available, continue without it
            pass
    
    def drop_dual_video(self, event):
        """Handle drag and drop for dual video"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.dual_video_path.set(files[0])
    
    def drop_reference_video(self, event):
        """Handle drag and drop for reference video"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.reference_video_path.set(files[0])
    
    def browse_dual_video(self):
        """Browse for dual person video"""
        filename = filedialog.askopenfilename(
            title="Select Dual Person Video",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.dual_video_path.set(filename)
    
    def browse_reference_video(self):
        """Browse for reference video"""
        filename = filedialog.askopenfilename(
            title="Select Reference Video",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.reference_video_path.set(filename)

    def validate_inputs(self):
        """Validate input files"""
        dual_path = self.dual_video_path.get()
        ref_path = self.reference_video_path.get()

        if not dual_path:
            messagebox.showerror("Error", "Please select a dual person video")
            return False

        if not ref_path:
            messagebox.showerror("Error", "Please select a reference video")
            return False

        if not os.path.exists(dual_path):
            messagebox.showerror("Error", f"Dual video file not found: {dual_path}")
            return False

        if not os.path.exists(ref_path):
            messagebox.showerror("Error", f"Reference video file not found: {ref_path}")
            return False

        return True

    def start_analysis(self):
        """Start the analysis in a separate thread"""
        if self.analysis_running:
            messagebox.showwarning("Warning", "Analysis is already running!")
            return

        if not self.validate_inputs():
            return

        # Disable the analyze button and start progress
        self.analyze_button.config(state='disabled')
        self.progress_bar.start()
        self.analysis_running = True
        self.progress_var.set("Starting analysis...")

        # Clear previous results
        self.results_text.delete(1.0, tk.END)

        # Start analysis in separate thread
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def run_analysis(self):
        """Run the analysis (called in separate thread)"""
        try:
            dual_path = self.dual_video_path.get()
            ref_path = self.reference_video_path.get()

            # Update progress
            self.message_queue.put(("progress", "Processing videos..."))

            # Perform analysis
            results = self.analyzer.compare_dual_walk(dual_path, ref_path)

            # Update progress
            self.message_queue.put(("progress", "Generating results..."))

            # Format results for display
            results_text = self.format_results(results)
            self.message_queue.put(("results", results_text))

            # Save outputs if requested
            if self.save_plot_var.get():
                self.message_queue.put(("progress", "Saving analysis plot..."))
                self.analyzer.plot_frame_analysis(results, "starwalk_analysis.png")
                self.message_queue.put(("info", "Analysis plot saved as: starwalk_analysis.png"))

            if self.save_video_var.get():
                self.message_queue.put(("progress", "Saving comparison video..."))
                self.analyzer.save_comparison_video(results, "starwalk_comparison.mp4")
                self.message_queue.put(("info", "Comparison video saved as: starwalk_comparison.mp4"))

            self.message_queue.put(("complete", "Analysis completed successfully!"))

        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.message_queue.put(("error", error_msg))

    def format_results(self, results):
        """Format analysis results for display"""
        person1_metrics = results['person1_metrics']
        person2_metrics = results['person2_metrics']
        winner = results['winner']

        output = []
        output.append("=" * 60)
        output.append("           STARWALK ANALYSIS RESULTS")
        output.append("=" * 60)
        output.append("")
        output.append(f"🏆 WINNER: {winner}")
        output.append(f"   Confidence Gap: {abs(person1_metrics.overall_confidence - person2_metrics.overall_confidence):.3f}")
        output.append("")
        output.append("📊 DETAILED METRICS COMPARISON:")
        output.append(f"{'Metric':<25} {'Person 1 (Left)':<15} {'Person 2 (Right)':<15} {'Better':<10}")
        output.append("-" * 70)

        metrics_comparison = [
            ("Similarity Score", person1_metrics.similarity_score, person2_metrics.similarity_score),
            ("Stride Consistency", person1_metrics.stride_consistency, person2_metrics.stride_consistency),
            ("Posture Stability", person1_metrics.posture_stability, person2_metrics.posture_stability),
            ("Arm Coordination", person1_metrics.arm_swing_coordination, person2_metrics.arm_swing_coordination),
            ("Overall Confidence", person1_metrics.overall_confidence, person2_metrics.overall_confidence)
        ]

        for metric_name, p1_score, p2_score in metrics_comparison:
            better = "Person 1" if p1_score > p2_score else "Person 2"
            output.append(f"{metric_name:<25} {p1_score:<15.3f} {p2_score:<15.3f} {better:<10}")

        output.append("")
        output.append("📈 PERFORMANCE GRADES:")
        output.append(f"Person 1 (Left):  {self.analyzer.get_performance_grade(person1_metrics.overall_confidence)}")
        output.append(f"Person 2 (Right): {self.analyzer.get_performance_grade(person2_metrics.overall_confidence)}")

        output.append("")
        output.append("📋 DETAILED ANALYSIS:")
        output.append(f"Person 1 (Left):")
        output.append(f"  • Similarity to Reference: {person1_metrics.similarity_score:.3f}")
        output.append(f"  • Stride Consistency: {person1_metrics.stride_consistency:.3f}")
        output.append(f"  • Posture Stability: {person1_metrics.posture_stability:.3f}")
        output.append(f"  • Arm Coordination: {person1_metrics.arm_swing_coordination:.3f}")
        output.append(f"  • Overall Score: {person1_metrics.overall_confidence:.3f}")

        output.append(f"Person 2 (Right):")
        output.append(f"  • Similarity to Reference: {person2_metrics.similarity_score:.3f}")
        output.append(f"  • Stride Consistency: {person2_metrics.stride_consistency:.3f}")
        output.append(f"  • Posture Stability: {person2_metrics.posture_stability:.3f}")
        output.append(f"  • Arm Coordination: {person2_metrics.arm_swing_coordination:.3f}")
        output.append(f"  • Overall Score: {person2_metrics.overall_confidence:.3f}")

        return "\n".join(output)

    def check_queue(self):
        """Check for messages from analysis thread"""
        try:
            while True:
                message_type, message = self.message_queue.get_nowait()

                if message_type == "progress":
                    self.progress_var.set(message)
                elif message_type == "results":
                    self.results_text.insert(tk.END, message)
                    self.results_text.see(tk.END)
                elif message_type == "info":
                    self.results_text.insert(tk.END, f"\n{message}\n")
                    self.results_text.see(tk.END)
                elif message_type == "error":
                    self.progress_bar.stop()
                    self.analyze_button.config(state='normal')
                    self.analysis_running = False
                    self.progress_var.set("Error occurred")
                    self.results_text.insert(tk.END, f"\n❌ {message}\n")
                    self.results_text.see(tk.END)
                    messagebox.showerror("Analysis Error", message)
                elif message_type == "complete":
                    self.progress_bar.stop()
                    self.analyze_button.config(state='normal')
                    self.analysis_running = False
                    self.progress_var.set("Analysis completed!")
                    self.results_text.insert(tk.END, f"\n✅ {message}\n")
                    self.results_text.see(tk.END)
                    messagebox.showinfo("Success", message)

        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.check_queue)

    def open_output_folder(self):
        """Open the output folder"""
        import subprocess
        import platform

        current_dir = os.getcwd()

        if platform.system() == "Windows":
            subprocess.Popen(f'explorer "{current_dir}"')
        elif platform.system() == "Darwin":  # macOS
            subprocess.Popen(["open", current_dir])
        else:  # Linux
            subprocess.Popen(["xdg-open", current_dir])

    def clear_all(self):
        """Clear all inputs and results"""
        self.dual_video_path.set("")
        self.reference_video_path.set("")
        self.results_text.delete(1.0, tk.END)
        self.progress_var.set("Ready to analyze")


def main():
    """Main function to run the UI"""
    root = tk.Tk()
    app = StarWalkUI(root)

    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
