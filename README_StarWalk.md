# StarWalk - Dual Person Walk Analysis System

StarWalk is an advanced computer vision system that analyzes and compares walking patterns between two people in a single video against a reference walk. It determines which person's gait is more similar to the reference and provides comprehensive metrics.

## 🌟 Features

- **Dual Person Detection**: Automatically processes videos with two people walking side by side
- **Smart Aspect Ratio Handling**: Converts 16:9 videos to dual 8:9 frames for optimal analysis
- **Reference Comparison**: Compares both people against a reference walk video
- **Comprehensive Metrics**: 5 different analysis metrics for detailed evaluation
- **Visual Analytics**: Generates plots and comparison videos
- **No LLM Dependency**: Pure computer vision and mathematical analysis

## 📊 Metrics Explained

### 1. **Similarity Score** (40% weight)
- Measures 3D Euclidean distance between pose landmarks
- Compares key body points: shoulders, elbows, wrists, hips, knees, ankles
- Range: 0.0 - 1.0 (higher = more similar to reference)

### 2. **Stride Consistency** (25% weight)
- Analyzes regularity of leg movement patterns
- Measures variance in knee positions over time
- Range: 0.0 - 1.0 (higher = more consistent stride)

### 3. **Posture Stability** (20% weight)
- Evaluates shoulder and hip alignment stability
- Calculates angle variance between body segments
- Range: 0.0 - 1.0 (higher = better posture control)

### 4. **Arm Swing Coordination** (15% weight)
- Measures correlation between left and right arm movements
- Analyzes wrist velocity patterns
- Range: 0.0 - 1.0 (higher = better coordination)

### 5. **Overall Confidence Score**
- Weighted combination of all metrics
- Final score determining the winner
- Range: 0.0 - 1.0 (higher = better overall performance)

## 🎯 Performance Grades

| Score Range | Grade | Description |
|-------------|-------|-------------|
| 0.9 - 1.0   | A+ ⭐⭐⭐⭐⭐ | Excellent |
| 0.8 - 0.9   | A ⭐⭐⭐⭐ | Very Good |
| 0.7 - 0.8   | B+ ⭐⭐⭐ | Good |
| 0.6 - 0.7   | B ⭐⭐ | Above Average |
| 0.5 - 0.6   | C+ ⭐ | Average |
| < 0.5       | C | Needs Improvement |

## 🚀 Installation

```bash
# Install required dependencies
pip install opencv-python mediapipe numpy matplotlib

# Clone or download the StarWalk files
# starwalk.py - Main analysis system
# example_usage.py - Usage examples
```

## 💻 Usage

### Command Line Interface

```bash
# Basic analysis
python starwalk.py dual_video.mp4 reference_video.mp4

# With plot and video output
python starwalk.py dual_video.mp4 reference_video.mp4 --save-plot --save-video

# Custom output paths
python starwalk.py dual_video.mp4 reference_video.mp4 \
    --save-plot --plot-path my_analysis.png \
    --save-video --video-path my_comparison.mp4
```

### Python API

```python
from starwalk import StarWalkAnalyzer

# Initialize analyzer
analyzer = StarWalkAnalyzer()

# Perform analysis
results = analyzer.compare_dual_walk("dual_video.mp4", "reference_video.mp4")

# Display results
analyzer.display_results(results)

# Generate plots and videos
analyzer.plot_frame_analysis(results, "analysis.png")
analyzer.save_comparison_video(results, "comparison.mp4")
```

## 📹 Video Requirements

### Dual Person Video
- **Aspect Ratio**: 16:9 recommended (automatically split into 8:9 for each person)
- **Content**: Two people walking side by side
- **Duration**: 5-30 seconds recommended
- **Quality**: Clear visibility of both people

### Reference Video
- **Aspect Ratio**: Any (automatically cropped to 8:9)
- **Content**: Single person walking (the "ideal" walk)
- **Duration**: 5-30 seconds recommended
- **Quality**: Clear visibility of the person

### General Requirements
- **Formats**: MP4, AVI, MOV, MKV
- **Lighting**: Good lighting conditions
- **Camera**: Stable camera (minimal shaking)
- **Pose**: People walking towards/away from camera or sideways

## 🔧 Technical Architecture

### Core Components

1. **MediaPipe Pose Detection**: Extracts 33 body landmarks per frame
2. **Aspect Ratio Handler**: Smart cropping and splitting of video frames
3. **Similarity Calculator**: 3D Euclidean distance computation
4. **Metrics Engine**: Multi-dimensional gait analysis
5. **Visualization System**: Plots and video generation

### Key Landmarks Used
- Shoulders (11, 12)
- Elbows (13, 14)
- Wrists (15, 16)
- Hips (23, 24)
- Knees (25, 26)
- Ankles (27, 28)
- Heels (29, 30)
- Foot indices (31, 32)

## 📈 Output Files

1. **Console Output**: Detailed metrics and winner announcement
2. **Analysis Plot**: Frame-by-frame scores and metrics comparison
3. **Comparison Video**: Side-by-side visualization with pose overlays

## 🎯 Use Cases

- **Fashion/Modeling**: Compare runway walk techniques
- **Dance Training**: Analyze choreography synchronization
- **Physical Therapy**: Monitor gait rehabilitation progress
- **Sports Training**: Technique comparison and improvement
- **Entertainment**: Fun social media content creation

## 🔍 Example Output

```
============================================================
           STARWALK ANALYSIS RESULTS
============================================================

🏆 WINNER: Person 1 (Left)
   Confidence Gap: 0.127

📊 DETAILED METRICS COMPARISON:
Metric                   Person 1 (Left)   Person 2 (Right)  Better    
----------------------------------------------------------------------
Similarity Score         0.742             0.651             Person 1  
Stride Consistency       0.823             0.734             Person 1  
Posture Stability        0.691             0.598             Person 1  
Arm Coordination         0.567             0.612             Person 2  
Overall Confidence       0.731             0.604             Person 1  

📈 PERFORMANCE GRADES:
Person 1 (Left):  B+ (Good) ⭐⭐⭐
Person 2 (Right): B  (Above Average) ⭐⭐
```

## 🤝 Contributing

Feel free to contribute improvements, bug fixes, or new features to the StarWalk system!

## 📄 License

This project is open source and available under the MIT License.
