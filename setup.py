#!/usr/bin/env python3
"""
StarWalk Setup Script
Automated setup for StarWalk dual person walk analysis system
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def create_virtual_environment():
    """Create a virtual environment"""
    venv_name = "starwalk_env"
    
    if os.path.exists(venv_name):
        print(f"📁 Virtual environment '{venv_name}' already exists")
        return venv_name
    
    print(f"🔧 Creating virtual environment: {venv_name}")
    
    # Create virtual environment
    if not run_command(f"python -m venv {venv_name}", "Creating virtual environment"):
        return None
    
    return venv_name

def get_activation_command(venv_name):
    """Get the activation command for the virtual environment"""
    system = platform.system()
    
    if system == "Windows":
        return f"{venv_name}\\Scripts\\activate"
    else:
        return f"source {venv_name}/bin/activate"

def install_requirements(venv_name):
    """Install required packages"""
    system = platform.system()
    
    if system == "Windows":
        pip_command = f"{venv_name}\\Scripts\\pip"
        python_command = f"{venv_name}\\Scripts\\python"
    else:
        pip_command = f"{venv_name}/bin/pip"
        python_command = f"{venv_name}/bin/python"
    
    # Upgrade pip first
    if not run_command(f"{python_command} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{pip_command} install -r requirements.txt", "Installing requirements"):
        return False
    
    # Try to install optional drag-and-drop support
    print("🔄 Installing optional drag-and-drop support...")
    run_command(f"{pip_command} install tkinterdnd2", "Installing tkinterdnd2 (optional)")
    
    return True

def create_run_scripts(venv_name):
    """Create convenient run scripts"""
    system = platform.system()
    
    if system == "Windows":
        # Create Windows batch file
        script_content = f"""@echo off
echo Starting StarWalk UI...
call {venv_name}\\Scripts\\activate
python starwalk_ui.py
pause
"""
        with open("run_starwalk.bat", "w") as f:
            f.write(script_content)
        print("✅ Created run_starwalk.bat")
        
        # Create command line script
        cmd_content = f"""@echo off
call {venv_name}\\Scripts\\activate
python starwalk.py %*
"""
        with open("run_starwalk_cli.bat", "w") as f:
            f.write(cmd_content)
        print("✅ Created run_starwalk_cli.bat")
        
    else:
        # Create Unix shell script
        script_content = f"""#!/bin/bash
echo "Starting StarWalk UI..."
source {venv_name}/bin/activate
python starwalk_ui.py
"""
        with open("run_starwalk.sh", "w") as f:
            f.write(script_content)
        os.chmod("run_starwalk.sh", 0o755)
        print("✅ Created run_starwalk.sh")
        
        # Create command line script
        cmd_content = f"""#!/bin/bash
source {venv_name}/bin/activate
python starwalk.py "$@"
"""
        with open("run_starwalk_cli.sh", "w") as f:
            f.write(cmd_content)
        os.chmod("run_starwalk_cli.sh", 0o755)
        print("✅ Created run_starwalk_cli.sh")

def print_usage_instructions(venv_name):
    """Print usage instructions"""
    system = platform.system()
    activation_cmd = get_activation_command(venv_name)
    
    print("\n" + "="*60)
    print("🎉 STARWALK SETUP COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    print("\n📋 HOW TO USE STARWALK:")
    
    print("\n1️⃣ OPTION 1: Use the Graphical Interface (Recommended)")
    if system == "Windows":
        print("   • Double-click: run_starwalk.bat")
        print("   • Or run: python starwalk_ui.py")
    else:
        print("   • Run: ./run_starwalk.sh")
        print("   • Or run: python starwalk_ui.py")
    
    print("\n2️⃣ OPTION 2: Use Command Line")
    if system == "Windows":
        print("   • Use: run_starwalk_cli.bat dual_video.mp4 reference_video.mp4")
    else:
        print("   • Use: ./run_starwalk_cli.sh dual_video.mp4 reference_video.mp4")
    
    print("\n3️⃣ OPTION 3: Manual Activation")
    print(f"   • Activate environment: {activation_cmd}")
    print("   • Run UI: python starwalk_ui.py")
    print("   • Run CLI: python starwalk.py dual_video.mp4 reference_video.mp4")
    
    print("\n📁 OUTPUT FILES:")
    print("   • starwalk_analysis.png - Analysis plots")
    print("   • starwalk_comparison.mp4 - Comparison video")
    
    print("\n📹 VIDEO REQUIREMENTS:")
    print("   • Dual video: Two people walking side by side (16:9 recommended)")
    print("   • Reference video: Single person walking (any aspect ratio)")
    print("   • Formats: MP4, AVI, MOV, MKV")
    print("   • Duration: 5-30 seconds recommended")
    
    print("\n🆘 NEED HELP?")
    print("   • Check README_StarWalk.md for detailed documentation")
    print("   • Run example_usage.py for usage examples")

def main():
    """Main setup function"""
    print("🌟 StarWalk Setup - Dual Person Walk Analysis System")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    venv_name = create_virtual_environment()
    if not venv_name:
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements(venv_name):
        print("❌ Failed to install requirements")
        sys.exit(1)
    
    # Create run scripts
    create_run_scripts(venv_name)
    
    # Print usage instructions
    print_usage_instructions(venv_name)

if __name__ == "__main__":
    main()
