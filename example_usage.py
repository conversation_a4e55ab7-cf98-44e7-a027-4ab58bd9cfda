#!/usr/bin/env python3
"""
Example usage of StarWalk analyzer
This script demonstrates how to use the StarWalk system for dual person walk analysis
"""

from starwalk import StarWalkAnalyzer
import os

def example_usage():
    """
    Example of how to use StarWalk analyzer
    """
    
    # Example video paths (replace with your actual video paths)
    dual_video_path = "path/to/dual_person_walk.mp4"  # Video with two people walking side by side
    reference_video_path = "path/to/reference_walk.mp4"  # Reference walk video
    
    # Check if files exist (for demo purposes)
    if not os.path.exists(dual_video_path):
        print("⚠️  Please update dual_video_path with your actual video file")
        print("   The dual video should contain two people walking side by side")
        return
    
    if not os.path.exists(reference_video_path):
        print("⚠️  Please update reference_video_path with your actual reference video")
        print("   The reference video should contain a single person walking")
        return
    
    # Initialize the analyzer
    print("🚀 Initializing StarWalk Analyzer...")
    analyzer = StarWalkAnalyzer()
    
    try:
        # Perform the analysis
        print("🔍 Starting dual person walk analysis...")
        results = analyzer.compare_dual_walk(dual_video_path, reference_video_path)
        
        # Display comprehensive results
        analyzer.display_results(results)
        
        # Generate and save analysis plots
        print("📊 Generating analysis plots...")
        analyzer.plot_frame_analysis(results, "starwalk_analysis.png")
        
        # Save comparison video
        print("🎥 Creating comparison video...")
        analyzer.save_comparison_video(results, "starwalk_comparison.mp4")
        
        # Access individual metrics if needed
        person1_metrics = results['person1_metrics']
        person2_metrics = results['person2_metrics']
        winner = results['winner']
        
        print(f"\n🏆 Final Result: {winner} wins!")
        print(f"   Person 1 Overall Score: {person1_metrics.overall_confidence:.3f}")
        print(f"   Person 2 Overall Score: {person2_metrics.overall_confidence:.3f}")
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")

def create_test_videos():
    """
    Instructions for creating test videos
    """
    print("📹 How to create test videos for StarWalk:")
    print("\n1. DUAL PERSON VIDEO (16:9 aspect ratio recommended):")
    print("   • Record two people walking side by side")
    print("   • Each person should occupy roughly half the frame")
    print("   • Ensure both people are clearly visible")
    print("   • Walking should be natural and consistent")
    print("   • Duration: 5-30 seconds recommended")
    
    print("\n2. REFERENCE VIDEO (any aspect ratio):")
    print("   • Record a single person walking")
    print("   • This represents the 'ideal' or 'target' walk")
    print("   • Should show clear walking motion")
    print("   • Duration: 5-30 seconds recommended")
    
    print("\n3. VIDEO REQUIREMENTS:")
    print("   • Format: MP4, AVI, MOV, or MKV")
    print("   • Clear visibility of people")
    print("   • Stable camera (minimal shaking)")
    print("   • Good lighting")
    print("   • People should be walking towards/away from camera or sideways")

if __name__ == "__main__":
    print("=" * 60)
    print("           STARWALK EXAMPLE USAGE")
    print("=" * 60)
    
    # Show instructions for creating test videos
    create_test_videos()
    
    print("\n" + "=" * 60)
    print("           RUNNING ANALYSIS")
    print("=" * 60)
    
    # Run the example
    example_usage()
