#!/usr/bin/env python3
"""
StarWalk - Dual Person Walk Analysis System
Processes a single video with two people walking and compares their gait patterns
to a reference walk, determining which person is more similar to the reference.
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from mediapipe import solutions
import math
import os
import argparse
from typing import Tuple, List, Dict, Optional
from dataclasses import dataclass

@dataclass
class WalkMetrics:
    """Container for walk analysis metrics"""
    similarity_score: float
    stride_consistency: float
    posture_stability: float
    arm_swing_coordination: float
    overall_confidence: float
    frame_scores: List[float]

class StarWalkAnalyzer:
    """Main class for dual person walk analysis"""
    
    def __init__(self):
        # Initialize MediaPipe Pose
        self.mp_pose = solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=2,
            enable_segmentation=False,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = solutions.drawing_utils
        
        # Key body landmarks for walk analysis
        self.key_landmarks = [
            11, 12,  # Should<PERSON>
            13, 14,  # <PERSON><PERSON><PERSON>
            15, 16,  # Wrists
            23, 24,  # Hip<PERSON>
            25, 26,  # <PERSON><PERSON>
            27, 28,  # <PERSON><PERSON>
            29, 30,  # <PERSON><PERSON>
            31, 32   # Foot indices
        ]
        
    def crop_video_to_aspect_ratio(self, frame: np.ndarray, target_ratio: float = 8/9) -> np.ndarray:
        """
        Crop frame to target aspect ratio (8:9 for vertical videos)
        """
        height, width = frame.shape[:2]
        current_ratio = width / height
        
        if current_ratio > target_ratio:
            # Video is too wide, crop width
            new_width = int(height * target_ratio)
            start_x = (width - new_width) // 2
            cropped_frame = frame[:, start_x:start_x + new_width]
        else:
            # Video is too tall, crop height
            new_height = int(width / target_ratio)
            start_y = (height - new_height) // 2
            cropped_frame = frame[start_y:start_y + new_height, :]
            
        return cropped_frame
    
    def split_frame_vertically(self, frame: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Split a 16:9 frame into two 8:9 frames (left and right person)
        """
        height, width = frame.shape[:2]
        mid_point = width // 2
        
        left_frame = frame[:, :mid_point]
        right_frame = frame[:, mid_point:]
        
        return left_frame, right_frame
    
    def extract_pose_landmarks(self, frame: np.ndarray) -> Optional[List]:
        """
        Extract pose landmarks from a single frame
        """
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.pose.process(frame_rgb)
        
        if results.pose_landmarks:
            return results.pose_landmarks.landmark
        return None
    
    def process_video_dual_person(self, video_path: str) -> Tuple[List, List, List]:
        """
        Process video with two people and extract landmarks for both
        Returns: (person1_landmarks, person2_landmarks, combined_frames)
        """
        cap = cv2.VideoCapture(video_path)
        person1_landmarks = []
        person2_landmarks = []
        combined_frames = []
        
        print(f"Processing dual person video: {video_path}")
        
        frame_count = 0
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
                
            # Split frame into left and right person
            left_frame, right_frame = self.split_frame_vertically(frame)
            
            # Extract landmarks for both people
            landmarks1 = self.extract_pose_landmarks(left_frame)
            landmarks2 = self.extract_pose_landmarks(right_frame)
            
            if landmarks1 and landmarks2:
                person1_landmarks.append(landmarks1)
                person2_landmarks.append(landmarks2)
                
                # Draw landmarks on both frames
                left_frame_copy = left_frame.copy()
                right_frame_copy = right_frame.copy()
                
                # Create dummy results objects for drawing
                class DummyResults:
                    def __init__(self, landmarks):
                        self.pose_landmarks = type('obj', (object,), {'landmark': landmarks})()
                
                self.mp_drawing.draw_landmarks(
                    left_frame_copy, 
                    DummyResults(landmarks1).pose_landmarks, 
                    self.mp_pose.POSE_CONNECTIONS
                )
                self.mp_drawing.draw_landmarks(
                    right_frame_copy, 
                    DummyResults(landmarks2).pose_landmarks, 
                    self.mp_pose.POSE_CONNECTIONS
                )
                
                # Combine frames horizontally
                combined_frame = np.hstack((left_frame_copy, right_frame_copy))
                combined_frames.append(combined_frame)
                
            frame_count += 1
            if frame_count % 30 == 0:
                print(f"Processed {frame_count} frames...")
        
        cap.release()
        print(f"Extracted landmarks for {len(person1_landmarks)} frames")
        return person1_landmarks, person2_landmarks, combined_frames
    
    def process_reference_video(self, video_path: str) -> Tuple[List, List]:
        """
        Process reference video and extract landmarks
        Returns: (reference_landmarks, reference_frames)
        """
        cap = cv2.VideoCapture(video_path)
        reference_landmarks = []
        reference_frames = []
        
        print(f"Processing reference video: {video_path}")
        
        frame_count = 0
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
                
            # Crop to 8:9 aspect ratio
            cropped_frame = self.crop_video_to_aspect_ratio(frame)
            
            # Extract landmarks
            landmarks = self.extract_pose_landmarks(cropped_frame)
            
            if landmarks:
                reference_landmarks.append(landmarks)
                
                # Draw landmarks
                frame_copy = cropped_frame.copy()
                class DummyResults:
                    def __init__(self, landmarks):
                        self.pose_landmarks = type('obj', (object,), {'landmark': landmarks})()
                
                self.mp_drawing.draw_landmarks(
                    frame_copy, 
                    DummyResults(landmarks).pose_landmarks, 
                    self.mp_pose.POSE_CONNECTIONS
                )
                reference_frames.append(frame_copy)
                
            frame_count += 1
            if frame_count % 30 == 0:
                print(f"Processed {frame_count} reference frames...")
        
        cap.release()
        print(f"Extracted reference landmarks for {len(reference_landmarks)} frames")
        return reference_landmarks, reference_frames

    def calculate_landmark_distance(self, landmarks1: List, landmarks2: List) -> float:
        """
        Calculate 3D Euclidean distance between corresponding landmarks
        """
        total_distance = 0.0
        valid_landmarks = 0

        for i in self.key_landmarks:
            if i < len(landmarks1) and i < len(landmarks2):
                l1, l2 = landmarks1[i], landmarks2[i]
                distance = math.sqrt(
                    (l1.x - l2.x) ** 2 +
                    (l1.y - l2.y) ** 2 +
                    (l1.z - l2.z) ** 2
                )
                total_distance += distance
                valid_landmarks += 1

        return total_distance / valid_landmarks if valid_landmarks > 0 else float('inf')

    def calculate_stride_consistency(self, landmarks: List) -> float:
        """
        Calculate stride consistency based on leg movement patterns
        """
        if len(landmarks) < 10:
            return 0.0

        left_knee_positions = []
        right_knee_positions = []

        for frame_landmarks in landmarks:
            if len(frame_landmarks) > 26:
                left_knee_positions.append(frame_landmarks[25].y)  # Left knee
                right_knee_positions.append(frame_landmarks[26].y)  # Right knee

        if len(left_knee_positions) < 5:
            return 0.0

        # Calculate variance in knee movement
        left_variance = np.var(left_knee_positions)
        right_variance = np.var(right_knee_positions)

        # Lower variance indicates more consistent stride
        consistency = 1.0 / (1.0 + (left_variance + right_variance))
        return min(consistency, 1.0)

    def calculate_posture_stability(self, landmarks: List) -> float:
        """
        Calculate posture stability based on shoulder and hip alignment
        """
        if len(landmarks) < 5:
            return 0.0

        shoulder_angles = []
        hip_angles = []

        for frame_landmarks in landmarks:
            if len(frame_landmarks) > 24:
                # Calculate shoulder angle (left shoulder to right shoulder)
                left_shoulder = frame_landmarks[11]
                right_shoulder = frame_landmarks[12]
                shoulder_angle = math.atan2(
                    right_shoulder.y - left_shoulder.y,
                    right_shoulder.x - left_shoulder.x
                )
                shoulder_angles.append(shoulder_angle)

                # Calculate hip angle
                left_hip = frame_landmarks[23]
                right_hip = frame_landmarks[24]
                hip_angle = math.atan2(
                    right_hip.y - left_hip.y,
                    right_hip.x - left_hip.x
                )
                hip_angles.append(hip_angle)

        if len(shoulder_angles) < 3:
            return 0.0

        # Calculate stability as inverse of angle variance
        shoulder_stability = 1.0 / (1.0 + np.var(shoulder_angles))
        hip_stability = 1.0 / (1.0 + np.var(hip_angles))

        return (shoulder_stability + hip_stability) / 2.0

    def calculate_arm_swing_coordination(self, landmarks: List) -> float:
        """
        Calculate arm swing coordination during walking
        """
        if len(landmarks) < 10:
            return 0.0

        left_arm_swings = []
        right_arm_swings = []

        for i in range(1, len(landmarks)):
            prev_frame = landmarks[i-1]
            curr_frame = landmarks[i]

            if len(prev_frame) > 16 and len(curr_frame) > 16:
                # Calculate arm swing velocity
                left_wrist_vel = math.sqrt(
                    (curr_frame[15].x - prev_frame[15].x) ** 2 +
                    (curr_frame[15].y - prev_frame[15].y) ** 2
                )
                right_wrist_vel = math.sqrt(
                    (curr_frame[16].x - prev_frame[16].x) ** 2 +
                    (curr_frame[16].y - prev_frame[16].y) ** 2
                )

                left_arm_swings.append(left_wrist_vel)
                right_arm_swings.append(right_wrist_vel)

        if len(left_arm_swings) < 3:
            return 0.0

        # Calculate coordination as correlation between arm movements
        correlation = np.corrcoef(left_arm_swings, right_arm_swings)[0, 1]
        coordination = abs(correlation) if not np.isnan(correlation) else 0.0

        return coordination

    def calculate_comprehensive_metrics(self, person_landmarks: List, reference_landmarks: List) -> WalkMetrics:
        """
        Calculate comprehensive walk analysis metrics
        """
        # Synchronize video lengths
        min_frames = min(len(person_landmarks), len(reference_landmarks))
        person_landmarks = person_landmarks[:min_frames]
        reference_landmarks = reference_landmarks[:min_frames]

        # Calculate frame-by-frame similarity scores
        frame_scores = []
        for p_landmarks, r_landmarks in zip(person_landmarks, reference_landmarks):
            distance = self.calculate_landmark_distance(p_landmarks, r_landmarks)
            # Normalize to 0-1 scale (higher is better)
            similarity = 1.0 / (1.0 + distance)
            frame_scores.append(similarity)

        # Calculate overall similarity score
        similarity_score = np.mean(frame_scores)

        # Calculate individual metrics
        stride_consistency = self.calculate_stride_consistency(person_landmarks)
        posture_stability = self.calculate_posture_stability(person_landmarks)
        arm_swing_coordination = self.calculate_arm_swing_coordination(person_landmarks)

        # Calculate overall confidence (weighted combination)
        overall_confidence = (
            similarity_score * 0.4 +
            stride_consistency * 0.25 +
            posture_stability * 0.2 +
            arm_swing_coordination * 0.15
        )

        return WalkMetrics(
            similarity_score=similarity_score,
            stride_consistency=stride_consistency,
            posture_stability=posture_stability,
            arm_swing_coordination=arm_swing_coordination,
            overall_confidence=overall_confidence,
            frame_scores=frame_scores
        )

    def compare_dual_walk(self, dual_video_path: str, reference_video_path: str) -> Dict:
        """
        Main function to compare dual person walk with reference
        """
        print("=== StarWalk Analysis Started ===")

        # Process dual person video
        person1_landmarks, person2_landmarks, dual_frames = self.process_video_dual_person(dual_video_path)

        # Process reference video
        reference_landmarks, reference_frames = self.process_reference_video(reference_video_path)

        if not person1_landmarks or not person2_landmarks or not reference_landmarks:
            raise ValueError("Failed to extract landmarks from one or more videos")

        print("\n=== Calculating Metrics ===")

        # Calculate metrics for both people
        person1_metrics = self.calculate_comprehensive_metrics(person1_landmarks, reference_landmarks)
        person2_metrics = self.calculate_comprehensive_metrics(person2_landmarks, reference_landmarks)

        # Determine winner
        winner = "Person 1 (Left)" if person1_metrics.overall_confidence > person2_metrics.overall_confidence else "Person 2 (Right)"

        results = {
            'person1_metrics': person1_metrics,
            'person2_metrics': person2_metrics,
            'winner': winner,
            'dual_frames': dual_frames,
            'reference_frames': reference_frames
        }

        return results

    def display_results(self, results: Dict):
        """
        Display comprehensive analysis results
        """
        person1_metrics = results['person1_metrics']
        person2_metrics = results['person2_metrics']
        winner = results['winner']

        print("\n" + "="*60)
        print("           STARWALK ANALYSIS RESULTS")
        print("="*60)

        print(f"\n🏆 WINNER: {winner}")
        print(f"   Confidence Gap: {abs(person1_metrics.overall_confidence - person2_metrics.overall_confidence):.3f}")

        print(f"\n📊 DETAILED METRICS COMPARISON:")
        print(f"{'Metric':<25} {'Person 1 (Left)':<15} {'Person 2 (Right)':<15} {'Better':<10}")
        print("-" * 70)

        metrics_comparison = [
            ("Similarity Score", person1_metrics.similarity_score, person2_metrics.similarity_score),
            ("Stride Consistency", person1_metrics.stride_consistency, person2_metrics.stride_consistency),
            ("Posture Stability", person1_metrics.posture_stability, person2_metrics.posture_stability),
            ("Arm Coordination", person1_metrics.arm_swing_coordination, person2_metrics.arm_swing_coordination),
            ("Overall Confidence", person1_metrics.overall_confidence, person2_metrics.overall_confidence)
        ]

        for metric_name, p1_score, p2_score in metrics_comparison:
            better = "Person 1" if p1_score > p2_score else "Person 2"
            print(f"{metric_name:<25} {p1_score:<15.3f} {p2_score:<15.3f} {better:<10}")

        print("\n📈 PERFORMANCE GRADES:")
        print(f"Person 1 (Left):  {self.get_performance_grade(person1_metrics.overall_confidence)}")
        print(f"Person 2 (Right): {self.get_performance_grade(person2_metrics.overall_confidence)}")

        print(f"\n📋 DETAILED ANALYSIS:")
        self.print_detailed_analysis("Person 1 (Left)", person1_metrics)
        self.print_detailed_analysis("Person 2 (Right)", person2_metrics)

    def get_performance_grade(self, confidence: float) -> str:
        """Convert confidence score to performance grade"""
        if confidence >= 0.9:
            return "A+ (Excellent) ⭐⭐⭐⭐⭐"
        elif confidence >= 0.8:
            return "A  (Very Good) ⭐⭐⭐⭐"
        elif confidence >= 0.7:
            return "B+ (Good) ⭐⭐⭐"
        elif confidence >= 0.6:
            return "B  (Above Average) ⭐⭐"
        elif confidence >= 0.5:
            return "C+ (Average) ⭐"
        else:
            return "C  (Needs Improvement)"

    def print_detailed_analysis(self, person_name: str, metrics: WalkMetrics):
        """Print detailed analysis for a person"""
        print(f"\n{person_name}:")
        print(f"  • Similarity to Reference: {metrics.similarity_score:.3f} ({self.get_metric_description(metrics.similarity_score, 'similarity')})")
        print(f"  • Stride Consistency: {metrics.stride_consistency:.3f} ({self.get_metric_description(metrics.stride_consistency, 'consistency')})")
        print(f"  • Posture Stability: {metrics.posture_stability:.3f} ({self.get_metric_description(metrics.posture_stability, 'stability')})")
        print(f"  • Arm Coordination: {metrics.arm_swing_coordination:.3f} ({self.get_metric_description(metrics.arm_swing_coordination, 'coordination')})")
        print(f"  • Overall Score: {metrics.overall_confidence:.3f}")

    def get_metric_description(self, score: float, metric_type: str) -> str:
        """Get descriptive text for metric scores"""
        descriptions = {
            'similarity': {
                0.8: "Very similar to reference",
                0.6: "Good similarity",
                0.4: "Moderate similarity",
                0.2: "Low similarity",
                0.0: "Poor similarity"
            },
            'consistency': {
                0.8: "Very consistent stride",
                0.6: "Good stride rhythm",
                0.4: "Moderate consistency",
                0.2: "Inconsistent stride",
                0.0: "Very inconsistent"
            },
            'stability': {
                0.8: "Excellent posture",
                0.6: "Good posture control",
                0.4: "Moderate stability",
                0.2: "Unstable posture",
                0.0: "Poor posture control"
            },
            'coordination': {
                0.8: "Excellent arm swing",
                0.6: "Good coordination",
                0.4: "Moderate coordination",
                0.2: "Poor coordination",
                0.0: "Very poor coordination"
            }
        }

        desc_dict = descriptions.get(metric_type, {})
        for threshold in sorted(desc_dict.keys(), reverse=True):
            if score >= threshold:
                return desc_dict[threshold]
        return "Needs improvement"

    def plot_frame_analysis(self, results: Dict, save_path: str = "frame_analysis.png"):
        """Plot frame-by-frame analysis"""
        person1_metrics = results['person1_metrics']
        person2_metrics = results['person2_metrics']

        plt.figure(figsize=(12, 8))

        # Plot frame scores
        plt.subplot(2, 1, 1)
        frames = range(len(person1_metrics.frame_scores))
        plt.plot(frames, person1_metrics.frame_scores, 'b-', label='Person 1 (Left)', linewidth=2)
        plt.plot(frames, person2_metrics.frame_scores, 'r-', label='Person 2 (Right)', linewidth=2)
        plt.title('Frame-by-Frame Similarity Scores', fontsize=14, fontweight='bold')
        plt.xlabel('Frame Number')
        plt.ylabel('Similarity Score')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Plot metrics comparison
        plt.subplot(2, 1, 2)
        metrics_names = ['Similarity', 'Stride\nConsistency', 'Posture\nStability', 'Arm\nCoordination', 'Overall\nConfidence']
        person1_scores = [
            person1_metrics.similarity_score,
            person1_metrics.stride_consistency,
            person1_metrics.posture_stability,
            person1_metrics.arm_swing_coordination,
            person1_metrics.overall_confidence
        ]
        person2_scores = [
            person2_metrics.similarity_score,
            person2_metrics.stride_consistency,
            person2_metrics.posture_stability,
            person2_metrics.arm_swing_coordination,
            person2_metrics.overall_confidence
        ]

        x = np.arange(len(metrics_names))
        width = 0.35

        plt.bar(x - width/2, person1_scores, width, label='Person 1 (Left)', color='blue', alpha=0.7)
        plt.bar(x + width/2, person2_scores, width, label='Person 2 (Right)', color='red', alpha=0.7)

        plt.title('Metrics Comparison', fontsize=14, fontweight='bold')
        plt.xlabel('Metrics')
        plt.ylabel('Score')
        plt.xticks(x, metrics_names)
        plt.legend()
        plt.grid(True, alpha=0.3, axis='y')
        plt.ylim(0, 1)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\n📊 Frame analysis plot saved as: {save_path}")
        plt.show()

    def save_comparison_video(self, results: Dict, output_path: str = "starwalk_comparison.mp4"):
        """Save comparison video with analysis overlay"""
        dual_frames = results['dual_frames']
        reference_frames = results['reference_frames']

        if not dual_frames or not reference_frames:
            print("No frames available for video creation")
            return

        # Resize reference frames to match dual frame height
        dual_height = dual_frames[0].shape[0]
        reference_resized = []

        for ref_frame in reference_frames:
            ref_height, ref_width = ref_frame.shape[:2]
            new_width = int(ref_width * dual_height / ref_height)
            resized_ref = cv2.resize(ref_frame, (new_width, dual_height))
            reference_resized.append(resized_ref)

        # Create output video
        min_frames = min(len(dual_frames), len(reference_resized))
        if min_frames == 0:
            print("No frames to process")
            return

        # Get dimensions for output video
        dual_width = dual_frames[0].shape[1]
        ref_width = reference_resized[0].shape[1]
        total_width = dual_width + ref_width + 20  # 20px gap

        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 30.0, (total_width, dual_height))

        for i in range(min_frames):
            # Create combined frame
            combined_frame = np.ones((dual_height, total_width, 3), dtype=np.uint8) * 255

            # Add dual frame
            combined_frame[:, :dual_width] = dual_frames[i]

            # Add reference frame
            ref_start = dual_width + 20
            combined_frame[:, ref_start:ref_start + ref_width] = reference_resized[i]

            # Add text overlay
            cv2.putText(combined_frame, "Dual Walk", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(combined_frame, "Reference", (ref_start + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            out.write(combined_frame)

        out.release()
        print(f"\n🎥 Comparison video saved as: {output_path}")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="StarWalk - Dual Person Walk Analysis")
    parser.add_argument("dual_video", help="Path to video with two people walking")
    parser.add_argument("reference_video", help="Path to reference walk video")
    parser.add_argument("--save-plot", action="store_true", help="Save analysis plot")
    parser.add_argument("--save-video", action="store_true", help="Save comparison video")
    parser.add_argument("--plot-path", default="starwalk_analysis.png", help="Path for analysis plot")
    parser.add_argument("--video-path", default="starwalk_comparison.mp4", help="Path for comparison video")

    args = parser.parse_args()

    # Validate input files
    if not os.path.exists(args.dual_video):
        print(f"Error: Dual video file not found: {args.dual_video}")
        return

    if not os.path.exists(args.reference_video):
        print(f"Error: Reference video file not found: {args.reference_video}")
        return

    try:
        # Initialize analyzer
        analyzer = StarWalkAnalyzer()

        # Perform analysis
        results = analyzer.compare_dual_walk(args.dual_video, args.reference_video)

        # Display results
        analyzer.display_results(results)

        # Save plot if requested
        if args.save_plot:
            analyzer.plot_frame_analysis(results, args.plot_path)

        # Save video if requested
        if args.save_video:
            analyzer.save_comparison_video(results, args.video_path)

        print("\n✅ Analysis completed successfully!")

    except Exception as e:
        print(f"\n❌ Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
