@echo off
echo ========================================
echo    StarWalk Setup for Windows
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Starting setup...
echo.

REM Run the setup script
python setup.py

echo.
echo Setup completed! You can now use StarWalk.
echo.
echo Quick Start:
echo 1. Double-click "run_starwalk.bat" to open the UI
echo 2. Or use "run_starwalk_cli.bat" for command line
echo.
pause
