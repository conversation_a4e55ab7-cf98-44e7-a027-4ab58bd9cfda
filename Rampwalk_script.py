#!/usr/bin/env python
# coding: utf-8

# In[2]:


import cv2
import numpy as np
import matplotlib.pyplot as plt
from mediapipe import solutions
import math
import os
import glob


# In[3]:


import base64
import vertexai
from vertexai.generative_models import GenerativeModel, Part, FinishReason
import vertexai.preview.generative_models as generative_models


# In[4]:


def get_latest_video_path(directory, extensions=[".mp4", ".avi", ".mov", ".mkv"]):
    # Get all files in the directory with the given extensions
    files = []
    for ext in extensions:
        files.extend(glob.glob(os.path.join(directory, f"*{ext}")))

    # Check if there are any files
    if not files:
        print("No video files found in the directory.")
        return None

    # Sort files by modification time
    files.sort(key=os.path.getmtime, reverse=True)

    # Get the path of the latest file
    latest_file = files[0]
    return latest_file

# Example usage
directory = r'/run/user/1001/gvfs/mtp:host=OnePlus_KALAMA-MTP_CID%3A0437_SN%3AD18B65C7_d18b65c7/Internal shared storage/DCIM/Camera'
latest_video_path = get_latest_video_path(directory)

if latest_video_path:
    print(f"The latest video file is: {latest_video_path}")
else:
    print("No video file found.")


# In[5]:


model_path = ""
print("""1. Aditya <PERSON>, 2. <PERSON><PERSON><PERSON>nd<PERSON>, 3. <PERSON><PERSON>ik <PERSON><PERSON><PERSON>, 4. Joker, 5. <PERSON>di, 6. Shahid <PERSON>,
7. Al<PERSON> <PERSON>, 8. <PERSON> Hadid, 9. G<PERSON> Hadid, 10. <PERSON> <PERSON>ner, 11. <PERSON>riti Sanon, 12. <PERSON>, 13. Zendaya""")
# Mapping choices to model paths
model_paths = {
    1: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Aditya_Roy.mp4",
    2: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Brahmanandham.mp4",
    3: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Hrithik_Roshan.mp4",
    4: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Joker.mp4",
    5: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Modi_1.mp4",
    6: r"/home/<USER>/RampWalk/Models_9_16/Models_Actors_9_16/Shahid_kapoor.mp4",
    7: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Alaya_F.mp4",
    8: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Bella_Hadid.mp4",
    9: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Gigi_Hadid.mp4",
    10: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Kendall_Jenner.mp4",
    11: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Kriti_Sanon.mp4",
    12: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Tara.mp4",
    13: r"/home/<USER>/RampWalk/Models_9_16/Model_Actresses_9_16/Zendaya.mp4"
}

# Getting user choice
choice = int(input("Enter your choice: "))

# Retrieving the model path based on user choice
model_path = model_paths.get(choice, "Invalid choice")

# Displaying the model path
if model_path != "Invalid choice":
    print(f"Model path: {model_path}")
else:
    print(model_path)


# In[6]:


# Paths to videos
video_path1 = model_path
video_path2 = latest_video_path
output_video_path = 'CombinedOutput.mp4'

# Initialize video captures
cap1 = cv2.VideoCapture(video_path1)
cap2 = cv2.VideoCapture(video_path2)

# Initialize MediaPipe Pose
mp_pose = solutions.pose
pose = mp_pose.Pose()
mp_drawing = solutions.drawing_utils


# In[7]:


# Function to extract frames and keypoints from a video
def extract_keypoints_and_draw(cap):
    keypoints_all_frames = []
    frames_with_keypoints = []

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = pose.process(frame_rgb)

        if results.pose_landmarks:
            keypoints = results.pose_landmarks.landmark
            keypoints_all_frames.append(keypoints)
            mp_drawing.draw_landmarks(frame, results.pose_landmarks, mp_pose.POSE_CONNECTIONS)

        frames_with_keypoints.append(frame)

    cap.release()
    return keypoints_all_frames, frames_with_keypoints

# Extract keypoints and frames from both videos
keypoints_video1, frames_video1 = extract_keypoints_and_draw(cap1)
keypoints_video2, frames_video2 = extract_keypoints_and_draw(cap2)

# Ensure both videos have the same number of frames
min_frames = min(len(keypoints_video1), len(keypoints_video2))
keypoints_video1 = keypoints_video1[:min_frames]
keypoints_video2 = keypoints_video2[:min_frames]
frames_video1 = frames_video1[:min_frames]
frames_video2 = frames_video2[:min_frames]


# In[8]:


# Function to calculate similarity score
def calculate_similarity_score(keypoints1, keypoints2):
    scores = []

    for kp1, kp2 in zip(keypoints1, keypoints2):
        frame_score = 0
        for k1, k2 in zip(kp1, kp2):
            frame_score += math.sqrt((k1.x - k2.x) ** 2 + (k1.y - k2.y) ** 2 + (k1.z - k2.z) ** 2)
        scores.append(frame_score / len(kp1))

    mean_score = np.mean(scores)
    normalized_score = 1 / (1 + mean_score)
    return normalized_score

# Calculate similarity score
similarity_score = calculate_similarity_score(keypoints_video1, keypoints_video2)
print(f'Similarity Score: {similarity_score:.2f}')


# In[9]:


# Function to calculate frame-by-frame similarity scores
def frame_similarity_scores(keypoints1, keypoints2):
    frame_scores = []

    for kp1, kp2 in zip(keypoints1, keypoints2):
        frame_score = 0
        for k1, k2 in zip(kp1, kp2):
            frame_score += math.sqrt((k1.x - k2.x) ** 2 + (k1.y - k2.y) ** 2 + (k1.z - k2.z) ** 2)
        frame_scores.append(frame_score / len(kp1))

    return frame_scores

# Calculate frame-by-frame similarity scores
frame_scores = frame_similarity_scores(keypoints_video1, keypoints_video2)
normalized_frame_scores = [1 / (1 + score) for score in frame_scores]

# Plot frame-by-frame similarity scores
plt.plot(normalized_frame_scores)
plt.title('Frame-by-Frame Similarity Scores')
plt.xlabel('Frame')
plt.ylabel('Similarity Score')
plt.show()


# In[10]:


# Function to combine and write video
def combine_and_write_video(frames1, frames2, output_path):
    height, width, _ = frames1[0].shape
    combined_width = width * 2

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, 30.0, (combined_width, height))

    for frame1, frame2 in zip(frames1, frames2):
        combined_frame = np.hstack((frame1, frame2))
        out.write(combined_frame)

    out.release()

# Combine frames and write to new video
combine_and_write_video(frames_video1, frames_video2, output_video_path)


# In[11]:


def generate_from_video(video_path, score):
    vertexai.init(project="avid-shape-424005-p2", location="us-central1")
    model = GenerativeModel(
        "gemini-1.5-flash-001",
    )

    # Read the video file
    with open(video_path, "rb") as video_file:
        video_data = video_file.read()

    video1 = Part.from_data(
        mime_type="video/mp4",
        data=video_data
    )

    text = f"""Give a dictionary as response with 'Confidence Rating', 'Color Description', 'Funny Description', 'Commentary' as keys
Confidence Rating:
Rate the confidence of the person on the right out of 5 based on their posture and expression. Consider their body language, stance, and facial expressions.

Color Description:
Describe the color of the clothes worn by the person on the right.

Funny Description:
Give a humorous description of the person on the right, strictly tailor the description according to {score} which is the similarity score with respect to left person, Avoid using the words 'channeling,' 'inner,' 'emulate', and 'runway'

Overall Commentary:
Provide an overall commentary on the person on the right, including their confidence rating out of 5, the color of their clothes, and a funny description. Ensure the commentary is detailed and avoids the words 'channeling,' 'inner,' and 'runway.'''"""

    generation_config = {
        "max_output_tokens": 8192,
        "temperature": 0.7,
        "top_p": 0.95,
    }

    safety_settings = {
        generative_models.HarmCategory.HARM_CATEGORY_HATE_SPEECH: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        generative_models.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        generative_models.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        generative_models.HarmCategory.HARM_CATEGORY_HARASSMENT: generative_models.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    }

    responses = model.generate_content(
        [video1, text],
        generation_config=generation_config,
        safety_settings=safety_settings,
        stream=True,
    )

    for response in responses:
        print(response.text, end="")


# In[12]:


# Example usage
response_dict = generate_from_video('/home/<USER>/RampWalk/CombinedOutput.mp4', score=similarity_score)
print(response_dict)


# In[ ]:




